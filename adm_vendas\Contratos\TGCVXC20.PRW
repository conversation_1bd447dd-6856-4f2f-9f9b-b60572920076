#include "totvs.ch"

#xtranslate SomaAM(<cAM>, <nQ>) => eval({|x, y| cAM:= x, aA := Array(y), aeval(aA, {|| cAM := If(Right(cAM, 2) == "12", soma1(Left(cAM, 4)) + "01", soma1(cAM))}), cAM}, <cAM>, <nQ>)
#xtranslate TiraAM(<cAM>, <nQ>) => eval({|x, y| cAM:= x, aA := Array(y), aeval(aA, {|| cAM := If(Right(cAM, 2) == "01", Tira1(Left(cAM, 4)) + "12", Tira1(cAM))}), cAM}, <cAM>, <nQ>)
#xtranslate CmptoAM( <v1> )     => Right(<v1>, 4) + Left(<v1>, 2)
#xtranslate AMtoCmp( <v1> )     => Right(<v1>, 2) + "/" + Left(<v1>, 4)

/* Projetos
TIADMVIN-3138
TIADMVIN-4354 INTEGRACAO DE PROPOSTA - TGCVA232

oFoto  C20 
    oAniver  C21  PQB 
    oFotoDet C22  PQC 
    oMetrica C23  PQA 
    oExcecao c24  PQG
*/


User Function Tgcvxc20
Return


	Class Tgcvxc20

		Data cCliente    as String
		Data cLoja       as String
		Data cAniver     as String
		Data cNomeCli    as String
		Data cA1_Email   as String
		Data cA1_xEmailF as String
		Data cA3_Email   as String
		Data cContato    as String

		Data cContrato as String
		Data cRevisa   as String

		Data aAniver    as Array
		Data aCNB       as Array
		Data aCNBRec    as Array
		Data aPropos    as Array
		Data aAmSave    as Array
		Data aCNBModDif as Array
		Data aCNBDiverg as Array
		Data aTipoFoto  as Array
		Data aCPNJsLS   as Array

		Data lIntegra   as Boolean
		Data lAjusteFin as Boolean
		Data lMudouVlr  as Boolean
		Data cMsgRet    as String
		Data cIndice    as String
		Data cDescInd   as String
		Data cPropos    as String
		Data dPropos    as Date
		Data dDtAss     as Date
		DAta nPerInd    as Numeric
		DAta nPerCN7    as Numeric
		Data cCompet    as String
		Data cNumCXK    as String
		Data oContrato
		Data cCodAgr    as String
		Data cXModal    as String

		Data lOnlyID    as Boolean
		Data lJobIniMes as Boolean
		Data lDownGrade as Boolean
		Data lRollBack  as Boolean
		Data lModelo    as Boolean
		Data lTela      as Boolean
		Data lAuditaLS  as Boolean

		Data aOcoProd   as Array
		Data aOcoFoto   as Array
		Data cTitProd   as String
		Data cTitFoto   as String


		Method New(cCliente, cLoja)
		Method Load()
		Method GetContrato()
		Method GetAniCtr()
		Method Save(lRevisa)
		Method ChkInactive()
		Method AtuPHS()
		Method Integra(cProposta)
		Method IncPHB(cProposta, cChamado)
		Method PosAniv(cProposta)
		Method IntIntera(cProposta, nPosTp)
		Method IntOfertas(cProposta, nPosTp)
		Method VldProposta(cProposta)
		Method Destroy()
		Method SavePZW()
		Method Indice()
		Method Reajuste(cAMAnive, cNumCXK)
		Method AuditaLS(cAMAnive)
		Method Reprecifica()
		Method AtuaCNB()
		Method ChkAlt()
		Method AtuMotivo()
		Method ChkCNB(nx,ny)
		Method IncCXL(ny, cPlanilha, cItem, cProduto, oAniver, nVlrAnt, cObs)
		Method IncP68(cPlanilha, cItem, cProduto, oAniver, nVlrUni)
		Method AltCNB(cPlanilha, cItem, nVlrUni)
		Method NovoCiclo()
		Method ProcMsg(cMsg)
		Method ProcLog(cMsg)
		Method EmailIntegra()
		Method ChkEmail()
		Method EmailNovoCiclo()
		Method EmailAuditaLS()
		Method AtuNiver(cFotogr, cChavFo, cMAAniv)
		Method RollBack(aProposta)
		Method ChkManual()
		Method ChkDiverge(aCNBDif)
		Method LoadModelo()
		Method EsticaFoto()
		Method ChkEstica()
		Method ChkOcorrencia(lProduto, lFoto)
		Method ChkProduto()
		Method ChkFoto()
		Method GeraCSV(cArqCSV)
		Method GrvArq(cArquivo, cLinha, lEnter)

	EndClass

Method New(cCliente, cLoja, cSituac) Class Tgcvxc20
	Local aAreaSA1 := SA1->(GetArea())

	::cCliente   := cCliente
	::cLoja      := cLoja
	::cAniver    := ""
	::cNomeCli   := ""
	::aAniver    := {}
	::aCNB       := {}
	::aCNBRec    := {}
	::aPropos    := {}
	::aAMSave    := {Left(Dtos(Date()), 6)}
	::aCNBModDif := {}
	::aCNBDiverg := {}
	::aTipoFoto  := {}
	::aCPNJsLS   := {}

	::cMsgRet    := ""
	::cContrato  := ""
	::cRevisa    := ""
	::cIndice    := SuperGetMV("TI_GCVINDC",.F.,"001")
	::cDescInd   := ""
	::cPropos    := ""
	::dPropos    := ctod("")
	::cCompet    := AmtoCMP(Left(dtoS(dDataBase), 6))
	::nPerInd    := 0
	::nPerCN7    := 0

	::cNumCXK    := ""
	::cCodAgr    := ""
	::cXModal    := ""

	::lMudouVlr  := .f.
	::lIntegra   := .f.
	::lAjusteFin := .f.
	::lJobIniMes := .f.
	::lDownGrade := .f.
	::lRollBack  := .F.
	::lModelo    := .F.
	::cContrato  := ""
	::oContrato  := NIL

	SA1->(DbSetOrder(1))
	SA1->(DBSeek(xFilial("SA1") + ::cCliente))
	::cNomeCli   := SA1->A1_NOME
	::cContato   := SA1->A1_CONTATO
	::cA1_Email  := SA1->A1_EMAIL
	::cA1_xEmailF:= SA1->A1_XEMAILF
	::cA3_Email  := ""
	::lOnlyID    := .t.
	::lTela      := .f.
	::lAuditaLS  := .f.

	If cSituac == NIL
		cSituac := "05"
	EndIf

	::cContrato  := Padr("CON" + ::cCliente, 15)
	::oContrato  := Tgcvxc13():New(::cContrato, cSituac)

	If SA3->(dbseek(xFilial("SA3") + SA1->A1_VEND))
		::cA3_Email := alltrim(SA3->A3_EMAIL)
	EndIf

	If PZW->(DbSeek(xFilial("PZW") + ::cCliente))
		::cIndice := PZW->PZW_INDICE
	EndIf
	CN6->(dbsetorder(1))
	CN6->(DbSeek(xFilial("CN7")+ ::cIndice))
	::cDescInd := CN6->CN6_DESCRI

	RestArea(aAreaSA1)

	::aOcoProd := {}
	::aOcoFoto := {}
	::cTitProd := "Contrato;Planilha;Item;Produto;Descrição;Tipo;Sequencia;Proposta;Codigo Metrica;Aniversario;Tipo Foto;Chave foto;Recorrente Contrato;Recorrente Foto;CalcPrice;Calculadora;ID ocorencia;Descrição ocorrencia"
	::cTitFoto := "Cliente,Produto,Descrição,Tipo,Sequencia,Proposta,Codigo Metrica,Aniversario,Descrição ocorrencia"

Return



Method Load(aProFoto) Class Tgcvxc20
	Local nx:= 0
	Local cTipoFoto := ""
	Local cChaveTP  := ""

	Self:GetContrato(aProFoto)
	Self:Indice()

	::aAniver  := {}

	For nx := 1 to len(::aTipoFoto)
		cTipoFoto := ::aTipoFoto[nx, 1]
		cChaveTP  := ::aTipoFoto[nx, 2]
		aadd(::aAniver, Tgcvxc21():New(Self, cTipoFoto, cChaveTP))
	Next

	For nx := 1 to Len(::aAniver)
		::aAniver[nx]:Load()
	Next

Return


Method GetContrato(aProFoto) Class Tgcvxc20
	Local aAreaCN9  := CN9->(GetArea())
	Local aAreaCNB  := CNB->(GetArea())
	Local aAreaADY  := ADY->(GetArea())
	Local aAreaPOV  := POV->(GetArea())
	Local aAreaPWG  := PWG->(GetArea())
	Local cChaveCNB := ""
	Local cContrato := "CON" + ::cCliente

	Local cProduto  := ""
	Local cPlanilha := ""
	Local cItem     := ""
	Local cTipIli   := ""
	Local nQuant    := 0
	Local nVlrUni   := 0
	Local nVlrTot   := 0
	Local cProposta := ""
	Local cProRev   := ""
	Local cFoldPro  := ""
	Local cItemPro  := ""
	Local cCodMet   := ""
	Local cAgrupa   := ""
	Local cModal    := ""
	Local nPOver    := 0
	Local nPosPai   := 0
	Local aPais     := {}
	Local aFilhos   := {}
	Local nx        := 0
	Local cProdPai  := ""
	Local cProdFilho:= ""
	Local nPTipo    := 0
	Local nPosPropos:= 0
	Local cSituac   := ""
	Local np        := 0
	Local np2       := 0
	Local nVlrServ  := 0
	Local cListaTipo:= SuperGetMV("TI_GCVISER", .F., "2")    //Lista dos tipos considerados na saastização 1-Ilimintado e 2-ID

	Default aProFoto := {}

	::cContrato := Padr(cContrato, 15)

	CN9->(dbSetOrder(8))
	CN9->(dbSeek(FWxFilial("CN9") + ::cContrato + Space(6)))

	::cRevisa   :=  CN9->CN9_REVISA

	CNB->(DbSetOrder(1))  //CNB_FILIAL+CNB_CONTRA+CNB_REVISA+CNB_NUMERO+CNB_ITEM

	cChaveCNB := xFilial("CNB") + ::cContrato + ::cRevisa
	CNB->(DbSeek(cChaveCNB))
	While CNB->(! Eof() .and. cChaveCNB == CNB_FILIAL + CNB_CONTRA + CNB_REVISA)

		cProduto  := CNB->CNB_PRODUT
		cPlanilha := CNB->CNB_NUMERO
		cItem     := CNB->CNB_ITEM
		cTipIli   := CNB->CNB_XTPILI
		cSituac   := CNB->CNB_SITUAC

		cProposta := CNB->CNB_PROPOS
		cProRev   := CNB->CNB_PROREV
		cFoldPro  := CNB->CNB_XFLDPR
		cItemPro  := CNB->CNB_PROITN
		nQuant    := CNB->CNB_QUANT
		nVlrUni   := CNB->CNB_VLUNIT
		nVlrTot   := CNB->CNB_VLTOT
		nVlrAnt   := CNB->CNB_VLUNIT

		If CNB->CNB_TIPREC == "2" // 1-recorrente e 2-pontual
			CNB->(DbSkip())
			Loop
		EndIf

		nPosPropos := aScan(aProFoto,{|x| AllTrim(x[1]) == cProposta})

		If Empty(nPosPropos)
			If ! CNB->CNB_SITUAC $ "AP"
				CNB->(DbSkip())
				Loop
			EndIf
		EndIf

		ADY->(dbSetOrder(1))   //ADY_FILIAL+ADY_PROPOS
		ADY->(dbSeek(FWxFilial("ADY") + cProposta ))

		cCodMet := ADY->ADY_XMETRI
		cAgrupa := ADY->ADY_XCODAG
		cModal  := ADY->ADY_XMODAL

		ADZ->(dbSetOrder(3)) // ADZ_FILIAL+ADZ_PROPOS+ADZ_REVISA+ADZ_FOLDER+ADZ_ITEM
		ADZ->(DbSeek(FWxFilial("ADZ") + cProposta + cProRev + cFoldPro + cItemPro))
		nPOver := ADZ->ADZ_XPOVER

		PT6->(DbSetOrder(1))
		If ! PT6->(dbSeek(xFilial("PT6") + cAgrupa + cModal)) .or. ! PT6->PT6_FOTOGR $ "2345"
			CNB->(DbSkip())
			Loop
		EndIf

		If ! U_GCVIIILI(cProduto, cAgrupa, cModal) //verifica se produto é intera ilimitado
			CNB->(DbSkip())
			Loop
		EndIf

		//BuscaServico
		If cTipIli $ cListaTipo
			nVlrServ := ProdVlrMens(cProposta, cItemPro, cProduto)
		EndIf

		If cTipIli == "1"  // ilimitado
			::lOnlyID  := .F.
		EndIf

		nPTipo := Self:GetAniCtr()

		aadd(::aCNB   , {::cContrato, cPlanilha, cItem, cProduto, cTipIli, cProposta, cProRev,  nQuant, nVlrUni, nVlrTot, nPOver, 0, PT6->PT6_FOTOGR, nPTipo, nVlrAnt, cSituac, 0, "", nVlrServ})
		aadd(::aCNBRec, CNB->(Recno()))
		aadd(::aPropos, {cProposta, cCodMet, cAgrupa, cModal})  // não permitir duplicidade em apropos

		cChavePOV := xFilial("POV") + cAgrupa + cModal + CNB->CNB_PRODUT
		POV->(dbSetOrder(3))
		If POV->(DbSeek(cChavePOV)) .and. "NODE" $ POV->POV_PROPAI
			nPosPai := aScan(aPais, { |x| x[1]==POV->POV_PROPAI + cTipIli})
			If Empty(nPosPai)
				aAdd(aPais, {POV->POV_PROPAI + cTipIli, 0, 0})
				nPosPai := Len(aPais)
			EndIF
			aPais[nPosPai, 2]++
			aadd(aFilhos, {CNB->CNB_PRODUT, POV->POV_PROPAI + cTipIli})
		EndIf


		CNB->(DbSkip())
	End

	For nx:= 1 to len(::aCNB)
		cProdFilho := ::aCNB[nx, 4]
		cProposta  := ::aCNB[nx, 6]

		np2 :=  aScan(::aPropos, {|x| x[1] == cProposta})
		cAgrupa := ::aPropos[np2, 3]
		cModal  := ::aPropos[np2, 4]

		np := aScan(aFilhos, {|x| x[1] == cProdFilho})
		If Empty(np)
			Loop
		EndIf
		cProdPai  := aFilhos[np, 2]
		np := aScan(aPais, {|x| x[1] == cProdPai})
		If Empty(np)
			Loop
		EndIf
		::aCNB[nx, 12] := aPais[np, 2]

		cChavePOV := xFilial("POV") + cAgrupa + cModal + Left(cProdPai, 15)
		POV->(dbSetOrder(3))
		If POV->(DbSeek(cChavePOV))
			::aCNB[nx, 17] := POV->POV_LIMFAT
			::aCNB[nx, 18] := POV->POV_PRODUT
		EndIf
	Next

	RestArea(aAreaPWG)
	RestArea(aAreaPOV)
	RestArea(aAreaADY)
	RestArea(aAreaCNB)
	RestArea(aAreaCN9)

Return


Static Function ProdVlrMens(cProposta, cItemPro, cProduto)

	Local nVlrServ  := U_GCVXC32B(cProposta, cItemPro, cProduto)

Return nVlrServ



Method GetAniCtr() Class Tgcvxc20
	Local cChaveTP  := ""
	Local nPTipo    := 0
	Local cAgrupa   := ADY->ADY_XCODAG
	Local cModal    := ADY->ADY_XMODAL
	Local cTabela   := ADY->ADY_TABELA
	Local cProduto  := CNB->CNB_PRODUT
	Local cTipoFoto := PT6->PT6_FOTOGR

	If cTipoFoto == "2"
		cChaveTP := "NOVO INTERA"
	ElseIf cTipoFoto == "3"
		cChaveTP := cTabela
	ElseIf cTipoFoto == "4"
		cChaveTP := cAgrupa + cModal
	ElseIf cTipoFoto == "5"
		cChaveTP := cProduto
	EndIf

	cChaveTP := Padr(cChaveTP, 15)

	nPTipo := Ascan(::aTipoFoto, {|x| x[1] + x[2] == cTipoFoto + cChaveTP})
	If nPTipo == 0
		aadd(::aTipoFoto, {cTipoFoto, cChaveTP})
		nPTipo := Len(::aTipoFoto)
	EndIf

Return nPTipo

Method Save(lRevisa) Class Tgcvxc20
	Local nx
	Default lRevisa := .F.

	If ! Empty(::cMsgRet)
		Return
	EndIf

	For nx := 1 to Len(::aAniver)
		If (::aAniver[nx]:lReaju .or. ::aAniver[nx]:lMudouFx ) .and. ! ::aAniver[nx]:lNewCicle
			lRevisa := .t.
		EndIf
	Next

	If ::lMudouVlr
		lRevisa := .t.
	EndIf

	If ! ::lIntegra
		Self:ProcMsg("Intera")
	EndIf
	Self:ProcLog("Atualizando fotografia")



	Begin Transaction

		For nx := 1 to Len(::aAniver)
			::aAniver[nx]:Save(lRevisa)
		Next
		Self:SavePZW()
		If lRevisa
			Self:ProcLog("Atualizando contrato")

			If ::lIntegra
				::oContrato:lCalcCron := .F.
				::oContrato:lRevisa   := .F.  // aproveita a revisão da integração
			EndIf
			If ::lJobIniMes .or. ::lDownGrade .or. ::lModelo
				CN9->(dbSetOrder(1))
				CN9->(dbSeek(FWxFilial("CN9") + ::cContrato + ::cRevisa))
				::oContrato:cSituac := CN9->CN9_SITUAC
				::oContrato:lCalcCron := .T.
				::oContrato:lRevisa   := .F.  // aproveita a revisão do job do inicio do mes
				::oContrato:aAMSave   := Self:aAmSave
			EndIf

			If ::lAjusteFin
				::oContrato:lCalcCron := .F.
				::oContrato:lRevisa   := .F.
			EndIf
			::oContrato:Save()
		EndIf
		Self:ChkInactive()

		Self:AtuPHS()

	End Transaction

	If ! Empty(::oContrato:cMsgErro)
		::cMsgRet:= ::oContrato:cMsgErro
	EndIf
	//Self:CNBToPQC()

Return


Method ChkInactive() Class Tgcvxc20
	Local cTipoFoto := ""
	Local cChaveTP  := ""
	Local aRecPQB   := {}
	Local aRecPQC   := {}
	Local cChavePQC := ""
	Local nx        := 0
	Local ny        := 0
	Local lInatPQC  := .T.
	Local cProduto  := ""
	Local nPc       := 0

	PQC->(DbSetOrder(12)) //PQC_FILIAL+PQC_SITUAC+PQC_STATUS+PQC_CLIENT+PQC_LOJA+PQC_FOTOGR+PQC_CHAVFO+PQC_PRODUT+PQC_TIPO+PQC_SEQMET
	PQB->(DbSetOrder(1)) //PQB_FILIAL+PQB_CODCLI+PQB_LOJA+PQB_STATUS
	PQB->(DbSeek(FWxFilial("PQB") + ::cCliente + ::cLoja + "A"))
	While PQB->(! Eof() .and. PQB_FILIAL+PQB_CODCLI+PQB_LOJA+PQB_STATUS == FWxFilial("PQB") + ::cCliente + ::cLoja + "A")
		cTipoFoto := PQB->PQB_FOTOGR
		cChaveTP  := PQB->PQB_CHAVFO

		lInatPQC  := .T.
		aRecPQC   := {}
		cChavePQC := FWxFilial("PQC") + "A" + "A" + ::cCliente + ::cLoja + cTipoFoto + cChaveTP
		If cTipoFoto == "2"
			If ! PQC->(DBSeek(cChavePQC))
				cChavePQC := FWxFilial("PQC") + "A" + "A" + ::cCliente + ::cLoja + " " +  Space(15)
				If ! PQC->(DBSeek(cChavePQC))
					PQB->(DbSkip())
					Loop
				EndIf
			EndIf
		Else
			If ! PQC->(DBSeek(cChavePQC))
				PQB->(DbSkip())
				Loop
			EndIf
		EndIf

		While PQC->(PQC_FILIAL+PQC_SITUAC+PQC_STATUS+PQC_CLIENT+PQC_LOJA+PQC_FOTOGR+PQC_CHAVFO) == cChavePQC .And. ! PQC->(Eof())
			aadd(aRecPQC, PQC->(Recno()))
			cProduto := PQC->PQC_PRODUT
			// verificar se o produto posicionado na foto está ativo
			nPc := aScan(::aCNB, {|x| x[4] == cProduto})
			If nPc > 0 // ativo ou faz parte do rollback
				If ! Empty(PQC->PQC_CONTRA)
					lInatPQC  := .F.
					aRecPQC   := {}
					Exit
				EndIf
			EndIf

			PQC->(DbSkip())
		End

		If lInatPQC
			aadd(aRecPQB, {PQB->(Recno()), aClone(aRecPQC)} )
		EndIf
		PQB->(DbSkip())
	End

	For nx:= 1 to len(aRecPQB)
		PQB->(DBGoto(aRecPQB[nx, 1]))
		PQB->(RecLock("PQB", .F.))
		PQB->PQB_STATUS := "I"
		PQB->(MsUnLock())
		For ny:= 1 to len(aRecPQB[nx, 2])
			PQC->(DBGoto(aRecPQB[nx, 2, ny]))
			PQC->(RecLock("PQC", .F.))
			PQC->PQC_STATUS := "I"
			PQC->PQC_SITUAC := "R"
			PQC->(MsUnLock())
		Next
	Next

Return

Method AtuPHS() Class Tgcvxc20
	Local nx      := 0
	Local lAtuPHS := .F.
	Local cID     := ""
	Local cAniver := ""
	Local cTPOper     := "000024"

	If ! ::lTela
		Return
	EndIf

	For nx := 1 to Len(::aAniver)
		If ::aAniver[nx]:lReaju
			lAtuPHS := .t.
			cAniver := ::aAniver[nx]:cAniver
			exit
		EndIf
	Next

	If ! lAtuPHS
		Return
	EndIf

	cID := U_GCIIA13H(cAniver, cTPOper)
	If ! Empty(cID)
		PHS->(DbSetOrder(6)) // PHS_FILIAL+PHS_IDPROC+PHS_CONTRA+PHS_REVISA+PHS_TPOPER+PHS_GRUPO+PHS_UNINEG
		If PHS->(DbSeek(FwxFilial("PHS") + cID + ::cContrato))
			U_GCIIA13L("2", "Fechamento Fotografia concluido", cID, __cUserId, cTPOper)
		EndIf
	EndIf


Return


Method Integra(cProposta) Class Tgcvxc20
	Local nPosTp    := 0
	Local cTipoFoto := ""

	nPosTp    := Self:PosAniv(cProposta)
	If Empty(nPosTp)
		::cMsgRet := "Proposta " + cProposta + " não encontrada com configuração de uso de fotografia!"
		Return
	EndIf
	cTipoFoto := ::aTipoFoto[nPosTp, 1]
	::lIntegra := .t.

	If cTipoFoto == "2"
		Self:IntIntera(cProposta, nPosTp)
	Else
		Self:IntOfertas(cProposta)
	EndIf

	//Self:IncPHB(cProposta)


Return


Method IncPHB(cProposta, cChamado) Class Tgcvxc20
	Local aAuxPHB := {}
	Local cTipo   := ""
	Default  cProposta := ""
	Default  cChamado  := ""

	If ! Empty(cProposta)
		cTipo  := "1"
		::oContrato:cObsPHB := "Integração proposta " + cProposta + CRLF + Alltrim(::oContrato:cObsPHB)
	ElseIf  ! Empty(cChamado)
		cTipo  := "2"
		::oContrato:cObsPHB := "Chamado " + cChamado + CRLF + Alltrim(::oContrato:cObsPHB)
	Else
		cTipo  := "3"
	EndIf

	aAuxPHB := {{"PHB_TPATEN", cTipo                },;
		{"PHB_PROPOS", cProposta            },;
		{"PHB_CHAMAD", cChamado             },;
		{"PHB_MOTIVO", ::oContrato:cMotPHB  },;
		{"PHB_OBSERV", ::oContrato:cObsPHB  },;
		{"PHB_CODUSR", __cUserId            },;
		{"PHB_USRNAM", UsrRetName(__cUserId)}}


	AADD(::oContrato:aIncPHB, aClone(aAuxPHB))
	aSize(aAuxPHB, 0)


Return
Method PosAniv(cProposta) Class Tgcvxc20
	Local np     := 0
	Local nPosTp := 0
	np        := ascan(::aCNB, {|x|  x[6] == cProposta})
	If ! Empty(np)
		nPosTp    := ::aCNB[np, 14]
	EndIf

Return nPosTp

Method IntIntera(cProposta, nPosTp) Class Tgcvxc20
	Local aArea     := GetArea()
	Local aAreaADY  := ADY->(GetArea())
	Local aAreaZAV  := ZAV->(GetArea())
	Local oAniver   := ::aAniver[nPosTp]

	Begin Sequence

		Self:VldProposta(cProposta, oAniver)

		Self:Indice()
		oAniver:oUltCli:MontaPQI(cProposta)
		oAniver:oMetrica:IncCliente(cProposta)
		oAniver:CriaCiclo(cProposta)
		oAniver:AtuMetrica()
		oAniver:oFotoDet:AtuFoto(cProposta)
		oAniver:oFotoDet:AtuFaixa()
		oAniver:oFotoDet:Reprecifica()
		Self:ChkAlt(cProposta)

	End Sequence

	RestArea(aAreaZAV)
	RestArea(aAreaADY)
	RestArea(aArea)

	If ! Empty(::cMsgRet)
		Return .f.
	EndIf

Return  .t.



Method IntOfertas(cProposta, nPosTp) Class Tgcvxc20
	Local aArea     := GetArea()
	Local aAreaADY  := ADY->(GetArea())
	Local aAreaZAV  := ZAV->(GetArea())
	Local nx        := 0
	Local np        := 0
	Local cProCNB   := ""
	Local oAniver   := NIL

	Begin Sequence

		For nx := 1 to len(::aAniver)
			oAniver   := ::aAniver[nx]

			np       := ascan(::aCNB, {|x|  x[14] == nx})
			If Empty(np)
				Loop
			EndIf
			cProCNB := ::aCNB[np, 6]
			If cProCNB != cProposta
				Loop
			EndIf

			Self:VldProposta(cProposta, oAniver)
			Self:Indice()
			oAniver:CriaCiclo(cProposta)
			oAniver:oFotoDet:AtuFoto(cProposta)
			oAniver:oFotoDet:Reprecifica()
			Self:ChkAlt(cProposta)
		Next

	End Sequence

	RestArea(aAreaZAV)
	RestArea(aAreaADY)
	RestArea(aArea)

	If ! Empty(::cMsgRet)
		Return .f.
	EndIf

Return  .t.

Method VldProposta(cProposta, oAniver) Class Tgcvxc20
	Local cAniver   := oAniver:cAniver

	::cMsgRet := ""

	ADY->(dbSetOrder(1))   //ADY_FILIAL+ADY_PROPOS
	If ! ADY->(dbSeek(FWxFilial("ADY") + cProposta ))
		::cMsgRet := "Proposta " + cProposta + " não encontrada!"
		Break
	EndIf

	::cPropos := cProposta
	::dPropos := ADY->ADY_DATA
	::cCompet := AmtoCMP(Left(dtoS(::dPropos), 6))
	If ::dPropos <  STOD("20200908")
		::cIndice := "004"
	EndIf
	::dDtAss  := ADY->ADY_XDTASS
	If Empty(::dDtAss)
		::dDtAss  := ADY->ADY_DTUPL
	EndIf

	::cCodAgr := ADY->ADY_XCODAG
	::cXModal := ADY->ADY_XMODAL

	PT6->(DbSetOrder(1))
	If ! PT6->(dbSeek(xFilial("PT6") + ::cCodAgr + ::cXModal))
		::cMsgRet := "Proposta " + cProposta + " não encontrada na configuração de fotografia (PT6)!"
		Break
	EndIf
	If ! PT6->PT6_FOTOGR $ "2345"
		::cMsgRet := "Proposta " + cProposta + " não tem controle de fotogafia!"
		Break
	EndIf

	If Empty(cAniver)
		oAniver:cAniver := Left(Dtos(ADY->ADY_DTUPL), 6)
	EndIF
	If Empty(oAniver:cAniver)
		oAniver:cAniver := Left(Dtos(Date()), 6)
	EndIf

Return

Method Destroy() Class Tgcvxc20
	Local nx

	For nx := 1 to Len(::aAniver)
		::aAniver[nx]:Destroy()
		FreeObj(::aAniver[nx])
		::aAniver[nx]:= nil
	Next

	aSize(::aAniver   , 0)
	aSize(::aCNB      , 0)
	aSize(::aCNBRec   , 0)
	aSize(::aPropos   , 0)
	aSize(::aAmSave   , 0)
	aSize(::aCNBModDif, 0)
	aSize(::aCNBDiverg, 0)
	aSize(::aTipoFoto , 0)

	::oContrato:Destroy()
	FreeObj(::oContrato)
	::oContrato:= NIL

Return

Method Indice() Class Tgcvxc20
	Local cChave  := ""
	Local aAreaCN7 := CN7->(GetArea())
	Local cCompet  := AMtoCmp(TiraAM(CmpToAM(::cCompet), 1))

	cChave := xFilial("CN7") + ::cIndice + cCompet

	CN7->(DbSetOrder(2)) // CN7_FILIAL+CN7_CODIGO+CN7_COMPET
	If CN7->(DbSeek(cChave))
		::nPerCN7 := CN7->CN7_VLREAL
		::nPerInd := CN7->CN7_VLREAL
		If ::nPerInd < 0
			::nPerInd := 0
		EndIf
	EndIf

	RestArea(aAreaCN7)

Return

Method SavePZW() Class Tgcvxc20
	Local aAreaCN6 := CN6->(GetArea())

	If PZW->(DbSeek(xFilial("PZW") + ::cCliente))
		Return
	EndIf

	RestArea(aAreaCN6)

	PZW->(RecLock("PZW",.T.))
	PZW->PZW_FILIAL := xFilial("PZW")
	PZW->PZW_CLIENT := ::cCliente
	PZW->PZW_DESCR  := ::cNomeCli
	PZW->PZW_CONTRA := ::cContrato
	PZW->PZW_PROPOS := ::cPropos
	PZW->PZW_DTPROP := ::dPropos
	PZW->PZW_INDICE := ::cIndice
	PZW->PZW_DESCIN := ::cDescInd
	PZW->PZW_STATUS := "1"
	PZW->(msunlock())

Return

/*
oFoto  C20 
    oAniver  C21  PQB 
    oFotoDet C22  PQC 
    oMetrica C23  PQA 
    oExcecao c24  PQG
*/

Method Reajuste(cAMAnive, cNumCXK) Class Tgcvxc20
	Local nx := 0
	Local oAniver

	::cCompet   := AMtoCmp(cAMAnive)
	If ! cNumCXK == NIL
		::cNumCXK := cNumCXK
	EndIf
	Self:Indice()

	For nx := 1 to len(::aAniver)
		oAniver := ::aAniver[nx]
		If ! oAniver:CanReaju(cAMAnive)
			Loop
		EndIf

		oAniver:cAniver   := cAMAnive
		oAniver:PutValue("PQB_PERIND", ::nPerInd)
		oAniver:lReaju    := .t.
		oAniver:lNewCicle := .f.
		oAniver:AtuMetrica()
		oAniver:oMetrica:ChkMulta()
		oAniver:oFotoDet:Reajuste()
		oAniver:oFotoDet:AtuFaixa()
		//oAniver:oFotoDet:Reprecifica()


	Next

Return

Method AuditaLS(cAMAudita) Class Tgcvxc20
	Local nx := 0
	Local oAniver

	::cCompet   := AMtoCmp(cAMAudita)
	::lAuditaLS := .T.

	For nx := 1 to len(::aAniver)
		oAniver := ::aAniver[nx]

		/*
		If ! oAniver:CanReaju(cAMAnive)
			Loop
		EndIf
		oAniver:cAniver   := cAMAnive
		oAniver:lReaju    := .t.
		oAniver:lNewCicle := .f.
		*/

		oAniver:AtuMetrica()
		oAniver:oMetrica:ChkMulta()
		oAniver:oFotoDet:AtuFaixa()

	Next

Return



Method Reprecifica() Class Tgcvxc20
	Local nx        := 0
	Local oAniver   := NIL

	For nx := 1 to len(::aAniver)
		oAniver   := ::aAniver[nx]
		oAniver:oFotoDet:Reprecifica()
	Next

Return


Method AtuaCNB() Class Tgcvxc20
	Local nx      := 0
	Local nPTipo
	Local oAniver
	Local oFotoDet
	Local cKeyName := ""
	Local cChave   := ""
	Local cProduto := ""
	Local cTipo    := ""
	Local nVlrMet  := 0
	Local nQtd     := 0
	Local lAtuCNB  := .f.
	Local nVlrServ := 0
	Local nVlrFim  := 0


	For nx:= 1 to len(::aCNB)
		cProduto  := ::aCNB[nx, 4]
		cTipo     := ::aCNB[nx, 5]
		nQtd      := ::aCNB[nx, 8]
		nVlrTot   := ::aCNB[nx, 10]

		nPTipo    := ::aCNB[nx, 14]
		If Empty(nQtd)
			Loop
		EndIf
		oAniver := ::aAniver[nPTipo]
		oFotoDet := oAniver:oFotoDet

		If cTipo == "1"
			cKeyName:= "PQC_PRODUT+PQC_TIPO+PQC_FXATUA"
			cChave := cProduto + cTipo + "S"
		Else
			cKeyName:= "PQC_PRODUT+PQC_TIPO"
			cChave := cProduto + cTipo
		EndIf

		If ! oFotoDet:Find(cKeyName, cChave)
			Loop
		EndIf
		nVlrMet := oFotoDet:GetValue("PQC_VLRMET")
		nVlrServ:= oFotoDet:GetValue("PQC_VLRSER")
		nVlrFim := U_IIVlrFim(nQtd, nVlrMet, nVlrServ)

		If !::aCNB[nx,  10] == nVlrFim
			::aCNB[nx,  9] := nVlrFim / nQtd
			::aCNB[nx, 10] := nVlrFim
			If ::oAniver:lReaju
				::aCNB[nx, 15] := nVlrFim / nQtd
			EndIf
			lAtuCNB := .t.
		EndIf

	Next

	If lAtuCNB
		Self:ChkAlt()
	EndIf


Return



Method ChkAlt(cProposta, lTelaIntera) Class Tgcvxc20
	Local nx      := 0
	Local nCXL    := 0
	Local aAux    := {}
	Local cObsAux := ""
	Local cObsPros:= ""
	Local cKeyName := ""
	Local cChave   := ""
	Local np       := 0
	Local lAtuCtr  := .f.
	Local nQtdCNB  := 0
	Default lTelaIntera := .F.



	For nx:= 1 to len(::aCNB)
		nQtdCNB := ::aCNB[nx, 8]
		If Empty(nQtdCNB)
			Loop
		EndIf
		If Self:ChkCNB(nx, @nCXL, cProposta)
			lAtuCtr := .T.
		EndIf
	Next

	If ! lAtuCtr
		Return
	EndIf

	If ! CXK->(DbSeek(xFilial("CXK") + ::cNumCXK)) .and. ! Empty(::cNumCXK)
		cKeyName := "CXK_NUMERO"
		cChave   := ::cNumCXK
		np       := PosItens(cKeyName, cChave, ::oContrato:aIncCXK)
		If Empty(np)
			aAux := {}
			aadd(aAux,{"CXK_FILIAL", xFilial("CXK")      })
			aadd(aAux,{"CXK_NUMERO", ::cNumCXK           })
			aadd(aAux,{"CXK_DATINI", dDataBase           })
			aadd(aAux,{"CXK_HORINI", Time()              })
			aadd(aAux,{"CXK_SITUAC", "C"                 })
			aadd(aAux,{"CXK_XCOMPE", ::cCompet           })
			aadd(aAux,{"CXK_XOBS"  , "Reajuste produtos Intera - TGCVXC20"})
			aadd(::oContrato:aIncCXK, aClone(aAux))
		EndIf
	EndIf

	cObsAux := ""
	For nx := 1 to len(::oContrato:aIncCXL)
		cObsAux += GetItens("CXL_XOBS", ::oContrato:aIncCXL[nx])
	Next

	For nx := 1 to len(::oContrato:aIncP68)
		cObsAux += GetItens("P68_XOBS" , ::oContrato:aIncP68[nx])
	Next

	If ! Empty(cProposta)
		cObsPros:=  MemoLine(::oContrato:cObsPHB, , 1) + CRLF
	EndIf
	::oContrato:cMotPHB := "257"
	Self:AtuMotivo()
	::oContrato:cObsPHB := cObsPros + cObsAux
	If ! lTelaIntera
		Self:IncPHB(cProposta, ::oContrato:cChamado)
	EndIf

Return


Method AtuMotivo() Class Tgcvxc20
	Local aLogCgt  := {}
	Local aLogCab  := {}
	Local aLogDet  := {}
	Local cObsFull := ""

	aLogCgt := U_GCVA301RL()

	If aLogCgt == NIl
		Return
	EndIf
	If Len(aLogCgt) < 2
		Return
	EndIf

	aLogCab := aLogCgt[1]
	aLogDet := aLogCgt[2]

	::oContrato:cChamado  := aLogCab[6, 2]
	::oContrato:cMotPHB   := aLogDet[1, 1, 1, 2]

	cObsFull := aLogDet[1, 1, 1, 4] + CRLF
	cObsFull += CRLF
	cObsFull += Repli("=", 40)  + CRLF
	cObsFull += "DETALHE:"      + CRLF
	cObsFull += Repli("=", 40)  + CRLF

	cObsFull += CRLF
	cObsFull += ::oContrato:cObsPHB

	::oContrato:cObsPHB := cObsFull

Return


Method ChkCNB(nx, nCXL, cProposta) Class Tgcvxc20
	Local cPlanilha := ::aCNB[nx,  2]
	Local cItem     := ::aCNB[nx,  3]
	Local cProduto  := ::aCNB[nx,  4]
	Local cTipo     := ::aCNB[nx,  5]
	Local nVlrUni   := ::aCNB[nx,  9]
	Local nVlrAnt   := ::aCNB[nx, 15]
	Local nPTipo    := ::aCNB[nx, 14]
	Local oAniver
	Local cObs      := ""
	Local cAmUlFat  := ""
	Local cAMProc   := CmpToAM(::cCompet)
	Local cCondicPH5:= ""

	oAniver := ::aAniver[nPTipo]

	CNB->(DbGoto(::aCNBRec[nx]))
	If ! Empty(cProposta) .and. cProposta == CNB->CNB_PROPOS
		Return .f.
	EndIf
	nP := Ascan(::aCNBModDif, {|x| x[1] == cProduto } )

	If Empty(nP)
		If ! oAniver:lReaju .and. nVlrUni == CNB->CNB_VLUNIT
			Return .f.
		EndIf
	Else
		nVlrUni := ::aCNBModDif[nP, 5]
		nVlrAnt := ::aCNBModDif[nP, 4]
	EndIf

	::lMudouVlr := .t.

	If cTipo == "1"
		cObs      += I18N("Produto #1 Intera Ilimitado", {cProduto}) + CRLF
	Else
		cObs      += I18N("Produto #1 Intera Nomeado (ID)", {cProduto}) + CRLF
	EndIf

	cAmUlFat := UltimoFatur(CNB->CNB_CONTRA,CNB->CNB_REVISA,CNB->CNB_NUMERO,CNB->CNB_ITEM,@cCondicPH5,cAMProc)

	If cAMProc <= cAmUlFat .and. !Empty(cAmUlFat)
		cAMProc := ProxFatur(cAmUlFat,cCondicPH5)
	EndIf

	::cCompet := AMToCmp(cAMProc)

	Self:IncCXL(@nCXL, cPlanilha, cItem, cProduto, oAniver, nVlrAnt, cObs)
	Self:IncP68(cPlanilha, cItem, cProduto, oAniver, nVlrUni, nVlrAnt, cObs, cTipo)
	Self:AltCNB(cPlanilha, cItem, nVlrUni)


Return .t.

Method IncCXL(nCXL, cPlanilha, cItem, cProduto, oAniver, nVlrAnt, cObs) Class Tgcvxc20
	Local cKeyName := ""
	Local cChave   := ""
	Local np       := 0
	Local aAux     := {}
	Local nPerInd  := ::nPerInd
	Local nPerCN7  := ::nPerCN7

	If ! oAniver:lReaju
		Return
	EndIf

	If nPerCN7 < 0
		cObs += I18N("Reajuste anual de #1%, indice #2 - #3 negativo #4", {Alltrim(Str(nPerInd, 10, 4)), ::cIndice, Alltrim(::cDescInd), Alltrim(Str(nPerCN7, 10, 4)) }) + CRLF
	Else
		cObs += I18N("Reajuste anual de #1% com indice #2 - #3", {Alltrim(Str(nPerInd, 10, 4)), ::cIndice, ::cDescInd }) + CRLF
	EndIf
	cObs += CRLF
	If Empty(::cNumCXK)
		::cNumCXK	:= GetSXeNum("CXK","CXK_NUMERO")
		ConfirmSX8()
	EndIf

	cKeyName := "CXL_NUMRJ+CXL_CONTRA+CXL_PLAN+CXL_ITEMPL"
	cChave   := ::cNumCXK  + ::cContrato + cPlanilha + cItem
	np       := PosItens(cKeyName, cChave, ::oContrato:aIncCXL)
	If Empty(np)
		nCXL++
		aAux := {}

		aadd(aAux,{"CXL_FILIAL", FwxFilial('CXL')                })
		aadd(aAux,{"CXL_NUMRJ" , ::cNumCXK                       })
		aadd(aAux,{"CXL_ITEM"  , StrZero(nCXL, Len(CXL->CXL_ITEM)) })
		aadd(aAux,{"CXL_CONTRA", ::cContrato                     })
		aadd(aAux,{"CXL_REVISA", ::cRevisa                       })
		aadd(aAux,{"CXL_PLAN"  , cPlanilha                       })
		aadd(aAux,{"CXL_ITEMPL", cItem                           })
		aadd(aAux,{"CXL_PRODUT", cProduto                        })
		aadd(aAux,{"CXL_XCOMPE", ::cCompet                       })
		aadd(aAux,{"CXL_INDICE", ::cIndice                       })
		aadd(aAux,{"CXL_SITUAC", "C"                             })
		aadd(aAux,{"CXL_DTANT" , CNB->CNB_DTREAJ                 })
		aadd(aAux,{"CXL_DTPREV", ctod("")                        })
		aadd(aAux,{"CXL_DTREAJ", dDataBase                       })
		aadd(aAux,{"CXL_DTPROX", ctod("")                        })
		aadd(aAux,{"CXL_VLANT" , CNB->CNB_VLUNIT                 })
		aadd(aAux,{"CXL_VLATU" , nVlrAnt                         })
		aadd(aAux,{"CXL_VLIND" , nPerInd                         })
		aadd(aAux,{"CXL_VLAPL" , nVlrAnt - CNB->CNB_VLUNIT       })
		aadd(aAux,{"CXL_XOBS"  , cObs                            })

		aadd(::oContrato:aIncCXL, aClone(aAux))
	Else
		PutItens("CXL_VLATU", nVlrAnt                  , ::oContrato:aIncCXL[np])
		PutItens("CXL_VLIND", nPerInd                  , ::oContrato:aIncCXL[np])
		PutItens("CXL_VLAPL", nVlrAnt - CNB->CNB_VLUNIT, ::oContrato:aIncCXL[np])
		PutItens("CXL_XOBS" , cObs                     , ::oContrato:aIncCXL[np])
	EndIf

Return


/*
000001 - Metrica
000002 - Multa 
000003 - Excecao
000004 - Manual 
000005 - reprecificacao
*/


Method IncP68(cPlanilha, cItem, cProduto, oAniver, nVlrUni, nVlrAnt, cObs, cTipo) Class Tgcvxc20
	Local cKeyName := ""
	Local cChave   := ""
	Local np       := 0
	Local aAux     := {}
	Local nPerInd  := 0
	Local cTipoP68 := ""
	Local cNumP68  := ""
	Local lOscila  := .f.
	Local nMetrica :=  oAniver:nMetrica
	Local nFaixaAnt:= oAniver:oFotoDet:nFaixaIni
	Local nFaixa   := oAniver:oFotoDet:nFaixa
	Local nTamFx   := FwGetSx3Cache("PQC_SEQMET", "X3_TAMANHO")
	Local cFaixaAnt:= StrZero(nFaixaAnt, nTamFx)
	Local cFaixaAtu:= StrZero(nFaixa, nTamFx)
	Local nTipoDiv := 0

	Local cFxMulta :=  oAniver:GetValue("PQB_FXMULT")
	Local cAMProc  :=  CmpToAM(::cCompet)

	If oAniver:lMudouFx .and. cTipo == "1"  // multa ou comprovação ou excecao
		lOscila := .t.
	Else
		If ! oAniver:lReaju .and. nVlrUni != nVlrAnt // reprecificação ou ajuste manual no contrato
			lOscila := .t.
		EndIf
	EndIF

	If ! lOscila
		Return
	EndIf

	If nVlrUni == nVlrAnt
		Return
	EndIf

	nPerInd   := nVlrUni / nVlrAnt
	nPerInd   := (nPerInd - 1) * 100

	If oAniver:lMudouFx  .and. cTipo == "1"  // multa ou comprovação ou excecao
		cObs      += I18N("Mudança de faixa da sequencia #1 para #2 com oscilação de #3% ", {nFaixaAnt, nFaixa, Alltrim(Str(nPerInd, 10, 4))}) + CRLF
		If oAniver:lMulta
			If oAniver:lIncAuditaLS
				cTipoP68 := "000008" // - Multa por auditoria
				cObs +="Auditoria LS"+ CRLF
			Else
				cTipoP68 := "000002" // - Multa
			EndIf
			cObs += "Metrica não declarada!" + CRLF
		ElseIf oAniver:oExcecao:lExcec15
			cTipoP68 := "000003" // - Excecao
		Else
			If oAniver:lIncAuditaLS
				cTipoP68 := "000009" // - Metrica por auditoria
				cObs +="Auditoria LS"+ CRLF
			Else
				cTipoP68 := "000001" // - Metrica
			EndIf

			If ! Empty(Val(cFxMulta))
				cObs += "Posicionado da faixa " + cFxMulta + "  anterior que estava com multa!"
			Else
				cObs += "Metrica declarada: R$ " + Alltrim(Transform(nMetrica ,"@E 999,999,999,999,999.99")) + CRLF
			EndIF
		EndIf
		If ! Empty(oAniver:oExcecao:ObsExcecao)
			cObs += "Observação exceção:" + CRLF
			cObs += oAniver:oExcecao:ObsExcecao + CRLF
		EndIf
		If ! Empty(oAniver:oMetrica:ObsMulta)
			cObs += "Observação multa:" + CRLF
			cObs += oAniver:oMetrica:ObsMulta + CRLF
		EndIf
	Else
		nP := Ascan(::aCNBModDif, {|x| x[1] == cProduto } )
		// reprecificação ou ajuste manual no contrato
		If nP > 0
			cTipoP68 := "000004" // - Manual
			cObs      += I18N("Alteração manual com oscilação de #1% ", {Alltrim(Str(nPerInd, 10, 4))}) + CRLF
		Else
			nP := Ascan(::aCNBDiverg, {|x| x[1] == cProduto } )
			// reprecificação ou ajuste manual no contrato
			If nP > 0

				nTipoDiv := ::aCNBDiverg[nP, 6]

				If nTipoDiv == "2" //Fotografia
					cTipoP68 := "000006" // - Ajuste de divergencia foto ajuste contrato
					cObs      += I18N("Ajuste de divergencia entre contrato e foto. Ajustado conforme fotografia de #1% ", {Alltrim(Str(nPerInd, 10, 4))}) + CRLF
				ElseIf nTipoDiv == "3"
					cTipoP68 := "000007" // - Ajuste de divergencia foto ajuste Manual
					cObs      += I18N("Ajuste de divergencia entre contrato e foto. Com alteração manual de #1% ", {Alltrim(Str(nPerInd, 10, 4))}) + CRLF
				EndIf
			Else
				cTipoP68 := "000005" // reprecificacao
				cObs      += I18N("Reprecificação com oscilação de #1% ", {Alltrim(Str(nPerInd, 10, 4))}) + CRLF
			EndIf
		EndIf
	EndIF
	cObs += CRLF


	cKeyName := "P68_CONTRA+P68_PLAN+P68_ITEMPL+P68_AMPROC "
	cChave   := ::cContrato + cPlanilha + cItem + CmpToAM(::cCompet)
	np       := PosItens(cKeyName, cChave, ::oContrato:aIncP68)
	If Empty(np)
		aAux := {}
		cNumP68	:= GetSXeNum("P68","P68_NUMSEQ")
		ConfirmSX8()
		aadd(aAux,{"P68_FILIAL", FwxFilial('P68')                })
		aadd(aAux,{"P68_CONTRA", ::cContrato                     })
		aadd(aAux,{"P68_REVISA", ::cRevisa                       })
		aadd(aAux,{"P68_PLAN"  , cPlanilha                       })
		aadd(aAux,{"P68_ITEMPL", cItem                           })
		aadd(aAux,{"P68_NUMSEQ", cNumP68                         })
		aadd(aAux,{"P68_AMPROC", cAMProc                         })
		aadd(aAux,{"P68_DTOSCI", dDataBase                       })
		aadd(aAux,{"P68_VLANT" , nVlrAnt                         })
		aadd(aAux,{"P68_VLATU" , nVlrUni                         })
		aadd(aAux,{"P68_TIPO"  , cTipoP68                        })
		aadd(aAux,{"P68_VLIND" , nPerInd                         })
		aadd(aAux,{"P68_VLAPL" , nVlrUni - nVlrAnt               })
		aadd(aAux,{"P68_PRODUT", cProduto                        })
		aadd(aAux,{"P68_XOBS"  , cObs                            })
		aadd(aAux,{"P68_FXANT" , cFaixaAnt                       })
		aadd(aAux,{"P68_FXATU" , cFaixaAtu                       })

		aadd(::oContrato:aIncP68, aClone(aAux))
	Else
		PutItens("P68_VLANT", nVlrAnt                  , ::oContrato:aIncP68[np])
		PutItens("P68_VLATU", nVlrUni                  , ::oContrato:aIncP68[np])
		PutItens("P68_VLIND", nPerInd                  , ::oContrato:aIncP68[np])
		PutItens("P68_VLAPL", nVlrUni - nVlrAnt        , ::oContrato:aIncP68[np])
		PutItens("P68_XOBS" , cObs                     , ::oContrato:aIncP68[np])
		PutItens("P68_FXANT", cFaixaAnt                , ::oContrato:aIncP68[np])
		PutItens("P68_FXATU", cFaixaAtu                , ::oContrato:aIncP68[np])

	EndIf

Return


Method AltCNB(cPlanilha, cItem, nVlrUni) Class Tgcvxc20
	Local cKeyName := ""
	Local cChave   := ""
	Local dUltReaj  := ctod("")


	If ! Empty(CNB->CNB_DTREAJ)
		dUltReaj := CNB->CNB_DTURJT
	EndIf

	cKeyName := "CNB_CONTRA+CNB_NUMERO+CNB_ITEM"
	cChave   := ::cContrato + cPlanilha + cItem
	np       := PosItens(cKeyName, cChave, ::oContrato:aAltCNB)
	If Empty(np)

		aAux := {}
		aadd(aAux,{"CNB_FILIAL", FwxFilial("CNB")                })
		aadd(aAux,{"CNB_CONTRA", ::cContrato                     })
		aadd(aAux,{"CNB_NUMERO", cPlanilha                       })
		aadd(aAux,{"CNB_ITEM"  , cItem                           })
		aadd(aAux,{"CNB_DTREAJ", dDataBase                       })
		aadd(aAux,{"CNB_DTURJT", dUltReaj                        })
		aadd(aAux,{"CNB_DTREAJ", dDataBase                       })
		aadd(aAux,{"CNB_RJTANT", CNB->CNB_VLUNIT                 })
		aadd(aAux,{"CNB_VLUNIT", nVlrUni                         })
		aadd(aAux,{"CNB_VLTOT" , nVlrUni  * CNB->CNB_QUANT       })

		aadd(::oContrato:aAltCNB, aClone(aAux))
	Else
		PutItens("CNB_VLUNIT", nVlrUni                  , ::oContrato:aAltCNB[np])
		PutItens("CNB_VLTOT" , nVlrUni  * CNB->CNB_QUANT, ::oContrato:aAltCNB[np])
	EndIf

Return



Method NovoCiclo() Class Tgcvxc20
	Local nx := 0
	Local oAniver
	Local lNewCicle := .F.

	For nx := 1 to len(::aAniver)
		oAniver := ::aAniver[nx]
		oAniver:NovoCiclo()
		oAniver:oMetrica:NovoCiclo()
		If oAniver:lNewCicle
			lNewCicle := .t.
		EndIf
	Next

Return lNewCicle

Method ProcMsg(cMsg)  Class Tgcvxc20
	If ! IsInCallStack('U_TIProcessa')
		Return
	EndIf
	U_TIProcMsg(cMsg)

Return

Method ProcLog(cMsg)  Class Tgcvxc20
	If ! IsInCallStack('U_TIProcessa')
		Return
	EndIf
	U_TIProcLog(cMsg)

Return

Method EmailIntegra() Class Tgcvxc20
	Local cPathHtm	 := '\web\tgcvw001_templates\'
	Local cFileas	 := 'ANEXO1.html'
	Local cMail      := ""
	Local cCCo    	 := SuperGetMV("TI_MAILCOP",.F.,"<EMAIL>")
	Local cFileTmp	 := CriaTrab(nil,.f.) + '.htm'
	Local dDtLimite  := ctod("")
	Local oHtmlas
	Local cCorpo
	Local nPosTp     := Self:PosAniv(::cPropos)
	Local cTipoFoto  := ""
	Local cChaveTP   := ""
	Local nx         := 0
	Local lIlimitado := .f.
	Local cContato   := ""


	nPosTp     := Self:PosAniv(::cPropos)
	If Empty(nPosTp)
		Return
	EndIf
	cTipoFoto  := ::aTipoFoto[nPosTp, 1]
	cChaveTP   := ::aTipoFoto[nPosTp, 2]

	If cTipoFoto != "2"  // email somente para novo intera
		Return
	EndIf

	// verificar se a proposta e refere a ID
	For nx := 1 to len(::aCNB)
		If ::aCNB[nx, 6] == ::cPropos .and. ::aCNB[nx, 5] == "1" // ilimitado
			lIlimitado := .t.
			Exit
		EndIf
	Next

	If ! lIlimitado  // não tem item ilimitado com a proposta a ser integrada
		Return
	EndIf


	If ! File(cPathHtm + cFileas)
		Return
	EndIf

	cMail := AdEmail(cMail, ::cA1_Email)
	cMail := AdEmail(cMail, ::cA1_xEmailF)
	cMail := AdEmail(cMail, ::cA3_Email)

	dDtLimite := Lastdate(MonthSum(::dDtAss, 3))
	cContato  := ::cNomeCli
	If Empty(cContato)
		cContato := ::cContato
	EndIf

	oHtmlas	:= TWFHtml():New(cPathHtm + cFileas)
	oHTMLAS:ValByName("cContato"  , cContato )
	oHTMLAS:ValByName("dDtprop"   , dDtLimite)
	oHtmlAS:Savefile(cPathHtm + cFileTmp)

	cCorpo := WFLoadFile(cPathHtm + cFileTmp)
	cCorpo := StrTran(cCorpo, CRLF, "")

	If ! GetEnvServer() $ "#TOTVS12;#TOTVS12_MI"
		cMail := "<EMAIL>"
		cCCo  := ""
	EndIf

	If U_xSendMail(cMail, "Bem Vindo a Totvs" , cCorpo,, .T.,,, .T.,, cCCo)

		PQB->(DbSetOrder(2)) //PQB_FILIAL+PQB_CODCLI+PQB_LOJA+PQB_FOTOGR+PQB_CHAVFO+PQB_STATUS
		If  PQB->(DbSeek(FWxFilial("PQB") + ::cCliente + ::cLoja + cTipoFoto + cChaveTP + "A"))
			PQB->(RecLock("PQB", .F.))
			PQB->PQB_MAILIN := "1"  // Flag de e-mail enviado.
			PQB->(MsUnLock())
		EndIf

	EndIf

	FErase(cPathHtm + cFileTmp)

	FreeObj(oHtmlas)
	oHtmlas := NIL

Return


Method ChkEmail() Class Tgcvxc20
	Local nx := 0
	Local oAniver

	For nx:= 1 to len(::aAniver)
		oAniver := ::aAniver[nx]

		If oAniver:lNewCicle
			Self:EmailNovoCiclo(nx)
		EndIf
	Next

Return

Method EmailNovoCiclo(nPosTp) Class Tgcvxc20
	Local cPathHtm	 := '\web\tgcvw001_templates\'
	Local cFileas	 := 'ANEXO6.html'
	Local cMail      := SuperGetMV("TI_MAILTO", .F., "<EMAIL>")
	Local cCCo    	 := SuperGetMV("TI_MAILCOP", .F., "<EMAIL>")
	Local cFileTmp	 := CriaTrab(nil,.f.) + '.htm'
	Local dDtLimite  := ctod("")
	Local oHtmlas
	Local cCorpo

	Local cTipoFoto  := ""
	Local cChaveTP   := ""
	Local nx         := 0
	Local lIlimitado := .f.
	Local cContato   := ""


	cTipoFoto  := ::aTipoFoto[nPosTp, 1]
	cChaveTP   := ::aTipoFoto[nPosTp, 2]

	If cTipoFoto != "2"  // email somente para novo intera
		Return
	EndIf

	// verificar se a proposta e refere a ID
	For nx := 1 to len(::aCNB)
		If ::aCNB[nx, 5] == "1" // ilimitado
			lIlimitado := .t.
			Exit
		EndIf
	Next

	If ! lIlimitado  // não tem item ilimitado
		Return
	EndIf


	If ! File(cPathHtm + cFileas)
		Return
	EndIf

	dDtLimite := Lastdate(dDataBase)
	cContato  := ::cNomeCli
	If Empty(cContato)
		cContato := ::cContato
	EndIf

	oHtmlas	:= TWFHtml():New(cPathHtm + cFileas)
	oHTMLAS:ValByName("cContato"  , cContato )
	oHTMLAS:ValByName("dDtprop"   , dDtLimite)
	oHtmlAS:Savefile(cPathHtm + cFileTmp)

	cCorpo := WFLoadFile(cPathHtm + cFileTmp)
	cCorpo := StrTran(cCorpo, CRLF, "")

	If ! GetEnvServer() $ "#TOTVS12;#TOTVS12_MI"
		cCCo  := ""
	Else
		cMail := "" 
		cMail := AdEmail(cMail, ::cA1_Email)
		cMail := AdEmail(cMail, ::cA1_xEmailF)
		cMail := AdEmail(cMail, ::cA3_Email)
	EndIf

	If U_xSendMail(cMail, "Totvs - Comprov. Anual" , cCorpo,, .T.,,, .T.,, cCCo)

		PQB->(DbSetOrder(2)) //PQB_FILIAL+PQB_CODCLI+PQB_LOJA+PQB_FOTOGR+PQB_CHAVFO+PQB_STATUS
		If  PQB->(DbSeek(FWxFilial("PQB") + ::cCliente + ::cLoja + cTipoFoto + cChaveTP + "A"))
			PQB->(RecLock("PQB", .F.))
			PQB->PQB_EMLA30 := "1"  // Flag de e-mail enviado.
			PQB->(MsUnLock())
		EndIf

	EndIf

	FErase(cPathHtm + cFileTmp)

	FreeObj(oHtmlas)
	oHtmlas := NIL

Return


Method EmailAuditaLS() Class Tgcvxc20
	Local cPathHtm	 := '\web\tgcvw001_templates\'
	Local cFileas	 := 'anexop40.html'
	Local cMail      := ""
	Local cCCo    	 := SuperGetMV("TI_MAILCOP", .F., "<EMAIL>")
	Local cFileTmp	 := CriaTrab(nil,.f.) + '.htm'
	Local oHtmlas
	Local cCorpo
	Local cTitulo    := ""
	Local nx         := 0

	If Empty(::aCPNJsLS) 
		Return 
	EndIf 

	aCnpjCtr := Aclone(::aCPNJsLS)

	If self:GetAniCtr() == 0
		Return
	EndIf

	If ::lOnlyID
		Return
	EndIf

	If ! File(cPathHtm + cFileas)
		Return
	EndIf

	cMail := AdEmail(cMail, ::cA1_Email)
	cMail := AdEmail(cMail, ::cA1_xEmailF)
	cMail := AdEmail(cMail, ::cA3_Email)

	cContato  := ::cNomeCli
	If Empty(cContato)
		cContato := ::cContato
	EndIf

	oHtmlas	:= TWFHtml():New(cPathHtm + cFileas)

	oHtmlas:ValByName( "cContato"  ,  Alltrim( cContato ))

	FOR nX:=1 TO LEN(aCnpjCtr)
		AADD( oHtmlas:ValByName("IT1.CNPJ")   	, Transform(aCnpjCtr[nX, 1] ,"@R 99.999.999/9999-99") )
		AADD( oHtmlas:ValByName("IT1.CDESC")  	, ALLTRIM(aCnpjCtr[nX, 2] ) )
	NEXT nX

	oHtmlas:Savefile(cPathHtm + cFileTmp)

	cCorpo := WFLoadFile(cPathHtm + cFileTmp)
	cCorpo := StrTran(cCorpo, CRLF, "")

	If ! GetEnvServer() $ "#TOTVS12;#TOTVS12_MI"
		cMail := "<EMAIL>"
		cCCo  := ""
	EndIf

	cTitulo := EncodeUtf8("TOTVS: INTERA ILIMITADO - NECESSIDADE DE COMPROVAÇÃO DE MÉTRICA DAS EMPRESAS REGISTRADAS NOS SOFTWARES")

	U_xSendMail(cMail, cTitulo , cCorpo,, .T.,,, .T.,, cCCo)

	FErase(cPathHtm + cFileTmp)

	FreeObj(oHtmlas)
	oHtmlas := NIL

Return


Method AtuNiver(cFotogr, cChavFo, cMAAniv) Class Tgcvxc20
	Local nPos := 0
	Local oAniver

	nPos := Ascan(::aTipoFoto, {|x| x[1] + x[2] == cFotogr + cChavFo})
	If Empty(nPos)
		Return
	EndIf

	oAniver   := ::aAniver[nPos]
	oAniver:AtuNiver(cMAAniv)

Return



Method RollBack(aProposta) Class Tgcvxc20
	Local nx := 0
	Local oAniver
	Local nPosTp    := 0
	Local cTipoFoto := ""

	If len(aProposta) > 0
		::lRollBack := .T.
	EndIf
	For nx := 1 to len(aProposta)

		cProposta := aProposta[nx, 1]
		nPosTp    := Self:PosAniv(cProposta)
		If Empty(nPosTp)
			Loop
		EndIf
		oAniver := ::aAniver[nPosTp]

		cTipoFoto := ::aTipoFoto[nPosTp, 1]
		If cTipoFoto == "2"
			oAniver:oUltCli:Load(cProposta)
			oAniver:oUltCli:RestorePQA(cProposta)
		EndIf
		oAniver:oFotoDet:Reprecifica()
	Next

Return

Method ChkManual() Class Tgcvxc20
	Local nX         := 0
	Local cProduto   := ""
	Local nQtde      := 0
	Local nVlrUni    := 0
	Local nVlrTot    := 0
	Local cTipo      := ""
	Local cSituac    := ""
	Local nOfertas   := 0
	Local aContratos := ::aCNB
	Local nPTipo     := 0
	Local oAniver    := NIL
	Local nLimAgru   := 0

	Local nP        := 0

	For nx:= 1 to len(aContratos)
		cProduto := aContratos[nx,  4]
		cTipo    := aContratos[nx,  5]
		nQtde    := aContratos[nx,  8]
		//nVlrUni  := aContratos[nx,  9]

		nPTipo   := aContratos[nx, 14]
		nOfertas := aContratos[nx, 12]
		cSituac  := aContratos[nx, 16]
		nLimAgru := aContratos[nx, 17]

		If ! cSituac $ "AP"
			Loop
		EndIf
		nP := Ascan(::aCNBModDif, {|x| x[1] == cProduto } )

		If Empty(nP)
			Loop
		EndIf

		nQtde   := ::aCNBModDif[nP, 3]
		nVlrUni := ::aCNBModDif[nP, 5]

		nVlrTot  := nQtde * nVlrUni

		oAniver := ::aAniver[nPTipo]
		If cTipo == "1"
			oAniver:oFotoDet:AtuPQCILI(cProduto, nQtde, nVlrUni, nVlrTot, nOfertas, nLimAgru)
		Else
			oAniver:oFotoDet:AtuPQCID(cProduto, nQtde, nVlrUni, nVlrTot, nOfertas, nLimAgru)
		EndIf

	Next

Return

Method ChkDiverge(aCNBDif) Class Tgcvxc20
	Local nX         := 0
	Local cProduto   := ""
	Local nQtde      := 0
	Local nVlrUni    := 0
	Local nVlrTot    := 0
	Local cTipo      := ""
	Local cSituac    := ""
	Local nOfertas   := 0
	Local nPTipo     := 0
	Local oAniver    := NIL

	Local nP        := 0

	::aCNBDiverg := aCNBDif

	For nx:= 1 to len(::aCNB)
		cProduto := ::aCNB[nx,  4]
		cTipo    := ::aCNB[nx,  5]
		nQtde    := ::aCNB[nx,  8]
		//nVlrUni  := ::aCNB[nx,  9]

		nPTipo   := ::aCNB[nx, 14]
		nOfertas := ::aCNB[nx, 12]
		cSituac  := ::aCNB[nx, 16]
		If ! cSituac $ "AP"
			Loop
		EndIf
		nP := Ascan(aCNBDif, {|x| x[1] == cProduto } )

		If Empty(nP)
			Loop
		EndIf

		oAniver := ::aAniver[nPTipo]
		nVlrTot := aCNBDif[nP, 7]

		If aCNBDif[nP, 6] == "1" .or. aCNBDif[nP, 6] == "3" //1=Contrato;2=Fotografia;3=Manual
			If aCNBDif[nP, 6] == "3"
				oAniver:lCNBDif := .T.
			EndIf
			nVlrUni := nVlrTot / nQtde

			If cTipo == "1"
				oAniver:oFotoDet:AtuPQCILI(cProduto, nQtde, nVlrUni, nVlrTot, nOfertas)
			Else
				oAniver:oFotoDet:AtuPQCID(cProduto, nQtde, nVlrUni, nVlrTot)
			EndIf
		EndIf
		If aCNBDif[nP, 6] == "2" .or. aCNBDif[nP, 6] == "3"
			oAniver:lCNBDif := .T.
			nVlrUni := nVlrTot / nQtde

			::aCNB[nX,  9] := nVlrUni
			::aCNB[nX, 10] := nVlrTot
			If oAniver:lReaju
				::aCNB[np, 15] := nVlrUni
			EndIf
		EndIf

	Next

	Self:ChkAlt()

Return

Method LoadModelo() Class Tgcvxc20
	Local oModel    := FWModelActive()
	Local oModCNA   := oModel:GetModel("CNADETAIL")
	Local oModCNB   := oModel:GetModel("CNBDETAIL")
	Local nx        := 0
	Local nY        := 0
	Local nQtdAnt   := 0
	Local nQtdAtu   := 0
	Local nVlrUAnt  := 0
	Local nVlrUAtu  := 0
	Local cProduto  := ""


	For nx := 1 to oModCNA:Length()
		oModCNA:GoLine(nx)
		If oModCNA:IsDeleted()
			Loop
		EndIf

		For ny := 1 to oModCNB:Length()
			oModCNB:GoLine(ny)
			If oModCNB:IsDeleted()
				Loop
			EndIf
			If !oModCNB:GetValue("CNB_SITUAC") $ "AP"
				Loop
			EndIf

			cProduto := oModCNB:GetValue("CNB_PRODUT")
			nQtdAnt  := oModCNB:GetValue("CNBQTDORIG")
			nQtdAtu  := oModCNB:GetValue("CNB_QUANT")
			nVlrUAnt := oModCNB:GetValue("CNBVLRORIG")
			nVlrUAtu := oModCNB:GetValue("CNB_VLUNIT")
			If Empty(nQtdAnt)
				Loop
			EndIf

			If nQtdAnt <> nQtdAtu .or. nVlrUAnt <> nVlrUAtu
				AADD(::aCNBModDif, {cProduto, nQtdAnt, nQtdAtu, nVlrUAnt, nVlrUAtu })
			EndIf
		Next
	Next

Return .f.

Static Function IsProdAlt(cCodProd, nVlrUni)
	Local oModel    := FWModelActive()
	Local oModCNA   := oModel:GetModel("CNADETAIL")
	Local oModCNB   := oModel:GetModel("CNBDETAIL")
	Local nx        := 0
	Local nY        := 0
	Local nQtdAnt   := 0
	Local nQtdAtu   := 0
	Local nVlrUAnt  := 0
	Local nVlrUAtu  := 0
	Local cProduto  := ""


	For nx := 1 to oModCNA:Length()
		oModCNA:GoLine(nx)
		If oModCNA:IsDeleted()
			Loop
		EndIf

		For ny := 1 to oModCNB:Length()
			oModCNB:GoLine(ny)
			If oModCNB:IsDeleted()
				Loop
			EndIf
			cProduto := oModCNB:GetValue("CNB_PRODUT")
			If cProduto != cCodProd
				Loop
			EndIf

			If !oModCNB:GetValue("CNB_SITUAC") $ "AP"
				Loop
			EndIf

			nQtdAnt  := oModCNB:GetValue("CNBQTDORIG")
			nQtdAtu  := oModCNB:GetValue("CNB_QUANT")
			nVlrUAnt := oModCNB:GetValue("CNBVLRORIG")
			nVlrUAtu := oModCNB:GetValue("CNB_VLUNIT")
			nVlrUni  := nVlrUAtu
			If nQtdAnt <> nQtdAtu .or. nVlrUAnt <> nVlrUAtu
				Return .t.
			EndIf
			Return .f.
		Next
	Next

Return .f.



Static Function AdEmail(cMail, cMailAd)

	cMailAd := Alltrim(cMailAd)
	If ! Empty(cMailAd)
		If ! Empty(cMail)
			cMail += ";"
		EndIf
		cMail += cMailAd
	EndIf

Return  cMail


Static Function GetItens(cCampo, aItens)
	Local np :=0
	Local uRet := NIL
	np := Ascan(aItens, {|x| Alltrim(x[1]) == Alltrim(cCampo)})
	If np > 0
		uRet := aItens[np, 2]
	EndIf
Return uRet

Static Function PutItens(cCampo, uConteudo, aItens)
	Local np :=0

	np := Ascan(aItens, {|x| Alltrim(x[1]) == Alltrim(cCampo)})
	If np > 0
		aItens[np, 2] := uConteudo
	EndIf

Return

Static Function PosItens(cKeyName, cChave, aDados)
	Local nx      := 0
	Local ny      := 0
	Local aCampos := {}
	Local cTemp   := ""

	aCampos   := Separa(cKeyName, "+")

	For nx := 1 to len(aDados)
		cTemp   := ""
		For ny := 1 to len(aCampos)
			cTemp += GetItens(aCampos[ny], aDados[nx])
		Next
		If cTemp == cChave
			Exit
		EndIf
	Next
	If nx > len(aDados)
		nx := 0
	EndIf

Return nx


Method EsticaFoto(cCodMet) Class Tgcvxc20
	Local nx := 0
	Local oAniver
	Local cObs  := ""
	Local cObs2 := ""


	For nx := 1 to len(::aAniver)
		oAniver := ::aAniver[nx]
		If ::aTipoFoto[nx, 1] != "2"
			Loop
		EndIf

		oAniver:oFotoDet:EsticaFoto(cCodMet, @cObs2)
		oAniver:oFotoDet:lRevNew := .t.
		If ! oAniver:oExcecao:Find("PQG_CODMOT", Padr("16", 6))
			cObs := "Inclusão de novas" + CRLF
			cObs += "Codigo de Metrica utilizada: " + cCodMet + CRLF
			cObs += cObs2

			oAniver:oExcecao:NewLine("PQG")
			oAniver:oExcecao:PutValue("PQG_FILIAL", xFilial("PQG")    )
			oAniver:oExcecao:PutValue("PQG_CLIENT", ::cCliente        )
			oAniver:oExcecao:PutValue("PQG_LOJA"  , ::cLoja           )
			oAniver:oExcecao:PutValue("PQG_CONTRA", ::cContrato       )
			oAniver:oExcecao:PutValue("PQG_REVISA", ::cRevisa         )
			oAniver:oExcecao:PutValue("PQG_VIGEDE", dDataBase         )
			oAniver:oExcecao:PutValue("PQG_VIGENC", stod("20491231")  )
			oAniver:oExcecao:PutValue("PQG_CODMOT", "16"              )
			oAniver:oExcecao:PutValue("PQG_DATGER", dDataBase         )
			oAniver:oExcecao:PutValue("PQG_MEMOD" , cObs              )
			oAniver:oExcecao:PutValue("PQG_SITUAC", "I"               )
		Else
			cObs := Alltrim(oAniver:oExcecao:GetValue("PQG_MEMOD")) + CRLF
			cObs += cObs2

			oAniver:oExcecao:PutValue("PQG_MEMOD" , cObs              )
		EndIf

	Next

Return


Method ChkEstica(cCodMet, cArqLog, cMsgVld) Class Tgcvxc20
	Local nx := 0
	Local oAniver
	Local oFotoDet
	Local cKeyName  := "PQC_CODMET"
	Local ny := 0
	Local nQtdFaixas := 0
	Local aProduto  := {}
	Local cProduto   := ""
	Local cTipo      := ""
	Local np         := 0


	cMsgVld := ""


	For nx := 1 to len(::aAniver)
		oAniver  := ::aAniver[nx]
		oFotoDet := ::aAniver[nx]:oFotoDet

		If ::aTipoFoto[nx, 1] != "2"
			Loop
		EndIf
		nQtdFaixas := oAniver:nQtdFaixas

		If ! oFotoDet:Find(cKeyName, cCodMet)
			Loop
		EndIf

		For ny := 1 to len(oFotoDet:aDados)
			oFotoDet:nAt := ny
			cProduto := oFotoDet:GetValue("PQC_PRODUT")
			cTipo    := oFotoDet:GetValue("PQC_TIPO"  )
			cSeqMet  := oFotoDet:GetValue("PQC_SEQMET")
			If cTipo != "1"
				Loop
			EndIf
			np := Ascan(aProduto, {|x| x[1] == cProduto })
			If np == 0
				aadd(aProduto, {cProduto, cSeqMet} )
			Else
				If cSeqMet > aProduto[np, 2]
					aProduto[np, 2] :=  cSeqMet
				EndIF
			EndIf
		Next

		For ny := 1 to len(aProduto)
			If aProduto[ny, 2] != StrZero(nQtdFaixas, 2)
				cMsgVld += Self:cCliente + ";" + aProduto[ny, 1] + ";faixa divergente produto:" + aProduto[ny, 2] + " foto: " + StrZero(nQtdFaixas, 2)  + CRLF
			EndIf
		Next

	Next

Return Empty(cMsgVld)



/*
    oFoto:= Tgcvxc20():New(cCliente, "00")
    oFoto:Load()
    oFoto:ChkProduto()
    oFoto:Destroy() 
    FreeObj(oFoto)
    oFoto := NIL 
*/    

// para cada produto da aCNB
// verificar se tem os campos obrigatorios, base, formula, valormetrica, preenchido na PQC
// verificar se tem divergencia valor recorrente entre item de contrato CNB e fotografia FOTO
// veriifcar se o valor metrica está calculdo corretamente conforme retorno calcprice
// verificar se tem produto em duplicidade na cnb
// verificar se o produto ilimitado está sem identificação de faixa 
Method ChkOcorrencia(lProduto, lFoto) Class Tgcvxc20

	Default lProduto := .t.
	Default lFoto    := .t.

	If lProduto
		Self:ChkProduto()
	EndIf

	If lFoto
		Self:ChkFoto()
	EndIf
Return


Method ChkProduto() Class Tgcvxc20
	Local nx := 0
	Local oAniver
	Local oFotoDet
	Local ny  := 0

	For nx := 1 to len(::aAniver)
		oAniver  := ::aAniver[nx]
		oFotoDet := ::aAniver[nx]:oFotoDet
		oFotoDet:ChkProduto()

		For ny := 1 to len(oFotoDet:aOcoProd)
			aadd(::aOcoProd, oFotoDet:aOcoProd[ny])
		Next
	Next

Return


// confrontar todos produto da metricas referente as propostas do contrato PO2, PO1
// verificar se o produto na pqc tem todas as faixas 
// Se existir
// verificar se tem formula, valorbase, valor recorrencia
Method ChkFoto() Class Tgcvxc20
	Local nx := 0
	Local oAniver
	Local oFotoDet
	Local ny  := 0

	For nx := 1 to len(::aAniver)
		oAniver  := ::aAniver[nx]
		oFotoDet := ::aAniver[nx]:oFotoDet
		oFotoDet:ChkFoto()
		For ny := 1 to len(oFotoDet:aOcoFoto)
			aadd(::aOcoFoto, oFotoDet:aOcoFoto[ny])
		Next
	Next

Return


Method GeraCSV(cArqCSV) Class Tgcvxc20
	Local nx := 0
	Local ny := 0
	Local cLinha := ""
	Local aLinha := {}
	Local cArqProd := ""
	Local cArqFoto := ""
	Local nRat := 0

	nRat := RAt("\", cArqCSV)
	If nRat > 0
		cArqProd := Left(cArqCSV, nRat ) + "ctr_" + subs(cArqCSV, nRat + 1)
		cArqFoto := Left(cArqCSV, nRat ) + "foto_" + subs(cArqCSV, nRat + 1)
	Else
		cArqProd := "ctr_" + cArqCSV
		cArqFoto := "foto_" + cArqCSV
	EndIf


	For nx := 1 to len(::aOcoProd)
		aLinha := aClone(::aOcoProd[nx])
		cLinha := ""
		For ny := 1 to len(aLinha)
			cLinha += aLinha[ny]
			If ny < Len(aLinha)
				cLinha +=";"
			EndIf
		Next

		If ! File(cArqProd)
			Self:GrvArq(cArqProd, ::cTitProd)
		EndIf
		Self:GrvArq(cArqProd, cLinha)
	Next


	For nx := 1 to len(::aOcoFoto)
		aLinha := aClone(::aOcoFoto[nx])
		cLinha := ""
		For ny := 1 to len(aLinha)
			cLinha += aLinha[ny]
			If ny < Len(aLinha)
				cLinha +=";"
			EndIf
		Next

		If ! File(cArqFoto)
			Self:GrvArq(cArqFoto, ::cTitFoto)
		EndIf
		Self:GrvArq(cArqFoto, cLinha)
	Next

Return



Method GrvArq(cArquivo, cLinha, lEnter) Class Tgcvxc20
	Local nHandle2 := 0
	Default lEnter := .t.
	If ! File(cArquivo)
		If (nHandle2 := MSFCreate(cArquivo,0)) == -1
			Return
		EndIf
	Else
		If (nHandle2 := FOpen(cArquivo,2)) == -1
			Return
		EndIf
	EndIf
	FSeek(nHandle2,0,2)
	If lEnter
		FWrite(nHandle2, cLinha + CRLF)
	Else
		FWrite(nHandle2, cLinha )
	EndIf
	FClose(nHandle2)
Return


/*/
	{PROJETO} - AvalUltFat
	@desc:		Avalia última competência de faturamento e retorna a próxima sugerida de acordo com a periodicidade.
	@author:	Carlos A. Queiroz
	@version: 	1.00
	@since: 	16/04/2024
/*/
/*
	TIADMVIN-4299 
*/
User Function AvUltFat(cCTRAtu, cRevAtu, cPlaATu, cItemAtu, cMAProc)

	Local cNewCompet := ""
	Local cAmUlFat   := ""
	Local cCondicPH5 := ""

	Default cCTRAtu  := ""
	Default cRevAtu  := ""
	Default cPlaATu  := ""
	Default cItemAtu := ""
	Default cMAProc  := ""

	If !Empty(cCTRAtu) .and. !Empty(cPlaATu) .and. !Empty(cItemAtu)
		cAMProc := CmpToAM(cMAProc)

		cAmUlFat := UltimoFatur(cCTRAtu,cRevAtu,cPlaATu,cItemAtu,@cCondicPH5,cAMProc)

		If cAMProc <= cAmUlFat .and. !Empty(cAmUlFat)
			cAMProc := ProxFatur(cAmUlFat,cCondicPH5)
		EndIf

		cNewCompet := AMToCmp(cAMProc)
	EndIf

Return cNewCompet


/*/
	{PROJETO} - UltimoFatur
	@desc:		Busca a última competência com o cronograma fechado.
	@author:	Carlos A. Queiroz
	@version: 	1.00
	@since: 	16/04/2024
/*/
/*
	TIADMVIN-4299 
*/
Static Function UltimoFatur(cCTRAtu,cRevAtu,cPlaATu,cItemAtu,cCondicPH5,cMesReaj)
	Local aAreaPH5   := PH5->(GetArea())
	Local aAreaSD1   := SD1->(GetArea())
	Local cUltimoFat := Space(6)
	Local cChvSD1    := ""
	Local cMesAux    := ""
	Local nVlrDev    := 0

	Default cCTRAtu    := ""
	Default cRevAtu    := ""
	Default cPlaATu    := ""
	Default cItemAtu   := ""
	Default cCondicPH5 := ""
	Default cMesReaj   := ""

	PH5->(DbSetOrder(17)) //PH5_FILIAL+PH5_CONTRA+PH5_REVISA+PH5_NUMERO+PH5_ITEM+PH5_ANOMES+PH5_SEQ+PH5_CLIENT+PH5_LOJA+PH5_CONDIC+PH5_CONDPG+PH5_NOTASE+PH5_MOEDA+PH5_GU
	//PH5->(DbSeek(FwxFilial("PH5") + CNB->(CNB_CONTRA + CNB_REVISA+ CNB_NUMERO + CNB_ITEM) + "999999", .T.))
	PH5->(DbSeek(FwxFilial("PH5") + cCTRAtu + cRevAtu + cPlaATu + cItemAtu + "999999", .T.))
	PH5->(DbSkip(-1))

	While !PH5->(BOF()) .And. PH5->(PH5_CONTRA+PH5_REVISA+PH5_NUMERO+PH5_ITEM) == cCTRAtu + cRevAtu + cPlaATu + cItemAtu
		If PH5->PH5_ANOMES < cMesReaj .and. Empty(cMesAux) //.and. !Empty(PH5->PH5_VLCARE)
			cMesAux := PH5->PH5_ANOMES
		EndIf

		If Empty(PH5->PH5_NUMMED)
			PH5->(DbSkip(-1))
			Loop
		EndIf

		SD1->(DbSetOrder(19)) // D1_FILIAL+D1_NFORI+D1_SERIORI+D1_FORNECE+D1_LOJA
		cChvSD1 :=  PH5->PH5_UNINEG + PH5->PH5_NOTA + PH5->PH5_SERIE + PH5->PH5_CLIENT + PH5->PH5_LOJA
		If !SD1->(DbSeek(cChvSD1))
			cUltimoFat := PH5->PH5_ANOMES
			cCondicPH5 := PH5->PH5_CONDIC
			Exit
		EndIf

		Do While !SD1->(EOF()) .and. cChvSD1 == SD1->(D1_FILIAL+D1_NFORI+D1_SERIORI+D1_FORNECE+D1_LOJA )
			If AllTrim(SD1->D1_ITEMORI) == AllTrim(PH5->PH5_ITEMNF)
				nVlrDev += SD1->D1_TOTAL
			EndIf
			SD1->(DbSkip())
		End Do

		If nVlrDev >= PH5->PH5_VLRFAT
			PH5->(DbSkip(-1))
			Loop
		EndIf

		cUltimoFat := PH5->PH5_ANOMES
		cCondicPH5 := PH5->PH5_CONDIC
		Exit

		PH5->(DbSkip(-1))
	EndDo

	If cMesAux > cUltimoFat
		cUltimoFat := cMesAux
	EndIf

	RestArea(aAreaPH5)

	RestArea(aAreaSD1)

Return cUltimoFat


/*/
	{PROJETO} - ProxFatur
	@desc:		Define a competência de faturamento que será gravada na CXL de acordo com a periodicidade.
	@author:	Carlos A. Queiroz
	@version: 	1.00
	@since: 	16/04/2024
/*/
/*
	TIADMVIN-4299 
*/
Static Function ProxFatur(cAmUlFat,cCondicPH5)

	Local cCompet    := ""
	Local nPeriod    := 0

	Default cAmUlFat   := ""
	Default cCondicPH5 := ""

	nPeriod  := val(cCondicPH5)+1

	cCompet := SomaAM(cAmUlFat, nPeriod)

Return cCompet

