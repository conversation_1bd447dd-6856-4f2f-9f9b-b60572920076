#Include "TOTVS.CH"
#INCLUDE "FWMVCDEF.CH"

// U_GCIIA11C(cCliente)              // Carga fotografia 
// U_GCIIA11N(cCliente)              // Novo ciclo intera
// U_GCIIA11R(cCliente, cAMAnive)    // Função para aplicar o reajuste
// U_GCIIA11M()                      // Função usado no modelo pos commit
    // U_GCIIA11I(cCliente, cProposta)   // Integração de proposta
    // U_GCIIA11K(cCliente, cProposta)   // RollBack de proposta
//GCIIA11S(cCliente, cAMAnive)           // Rotina de fechamento de auditoria ls
/*
{Protheus.doc} GCIIA11
Funcoes com uso da classe novo intera

TIADMVIN-3138

*/


/*
{Protheus.doc} GCIIA11I
Função de integração de proposta intera
*/


User Function GCIIA11I(cCliente, cLoja, cContrato, cSituac)
	Local cMsgRet := ""
    Local oFoto
    Local cProposta := ADY->ADY_PROPOS 

    If ! u_GcxF2Fot(ADY->ADY_XCODAG, ADY->ADY_XMODAL) // essa função posiciona a PT6
        Return 
    EndIf 

    oFoto:= Tgcvxc20():New(cCliente, cLoja, cSituac)
    oFoto:Load()
    If oFoto:PosAniv(cProposta) > 0
        oFoto:Integra(cProposta)
        oFoto:Save()
        oFoto:EmailIntegra()
    EndIf
	cMsgRet:= oFoto:cMsgRet

	oFoto:Destroy() 
	FreeObj(oFoto)
	oFoto := NIL 

Return cMsgRet


/*
{Protheus.doc} GCIIA11R
Função para abrir novo ciclo na fotografia

*/

User Function GCIIA11N(cCliente)
	Local cLoja     := "00"
	Local cMsgRet   := ""
    Local oFoto   
    
    oFoto:= Tgcvxc20():New(cCliente, cLoja)
    oFoto:Load()
    If oFoto:NovoCiclo()
        oFoto:Save()
        
        DbSelectArea("PQG")
		PQG->(dBSetOrder(2))
        If !PQG->(DbSeek(xFilial("PQG")+"A"+cCliente+cLoja+"11    "))
            oFoto:ChkEmail()
        EndIf
    EndIf 
    
    cMsgRet:= oFoto:cMsgRet

	oFoto:Destroy() 
	FreeObj(oFoto)
	oFoto := NIL 

Return cMsgRet


/*
{Protheus.doc} GCIIA11R
Função para realizar o reajuste na fotografia e item intera

*/

User Function GCIIA11R(cCliente, cAMAnive, cNumCXK)
	Local cLoja     := "00"
	Local cMsgRet   := ""
    Local oFoto   
    Default cNumCXK := ""

    oFoto:= Tgcvxc20():New(cCliente, cLoja)
    oFoto:Load()
    //oFoto:VldReajuste()
    oFoto:Reajuste(cAMAnive, cNumCXK)
    oFoto:ChkAlt()
    oFoto:Save(.t.)
    

    cMsgRet:= oFoto:cMsgRet

	oFoto:Destroy() 
	FreeObj(oFoto)
	oFoto := NIL 

Return cMsgRet

/*
{Protheus.doc} GCIIA11S
Função para realizar o Fechamento da auditori de LS

*/

User Function GCIIA11S(cCliente, cAMAnive)
	Local cLoja     := "00"
	Local cMsgRet   := ""
    Local oFoto   
    Local lRevisa   := .T.
    

    oFoto:= Tgcvxc20():New(cCliente, cLoja)
    oFoto:Load()
    oFoto:AuditaLS(cAMAnive)
    oFoto:ChkAlt()

    If Empty(oFoto:oContrato:aAltCNB)
        lRevisa := .F.
    EndIf
    oFoto:Save(lRevisa)
    

    cMsgRet:= oFoto:cMsgRet

	oFoto:Destroy() 
	FreeObj(oFoto)
	oFoto := NIL 

Return cMsgRet


/*
{Protheus.doc} GCIIA11C
Função de carga de fotografia de Intera

*/

User Function GCIIA11C(cCliente) 
	Local cMsg      := ""
	Local cMsgRet   := ""
    Local cTMP      := ""
    Local aArea     := GetArea()
    
	cTMP := MtaQry(cCliente)
	If Empty(cTMP)
        Return "Query não trouxe nada!!!"
    EndIf

	cMsg += "Cliente " + cCliente +  CRLF

    While  (cTMP)->(! Eof())
		cProposta := (cTMP)->ADY_PROPOS

		cMsgRet := U_GCIIA11I(cCliente, cProposta)
		If ! Empty(cMsgRet)
			cMsg += "     Proposta " + cProposta + " Erro -> " + cMsgRet + CRLF	
		Else 
			cMsg += "     Proposta " + cProposta + " OK" +  CRLF	
		EndIf 
        
        (cTMP)->(DbSkip())
    End
    (cTMP)->(DbCloseArea())
    
    RestArea(aArea)   

Return cMsg

Static Function MtaQry(cCliente)
    Local cQuery  := ""
    Local cTMP    := GetNextAlias()
    Local cNivel  := GetMV("TI_NVINTI",,"0352")
	Local cLoja   := "00"

    cQuery := " "
    cQuery += " SELECT R_E_C_N_O_ RECNO, ADY_PROPOS " 
	cQuery += "  FROM ADY000 " 
    cQuery += " WHERE ADY_FILIAL = ' ' " 
    cQuery += "   AND ADY_CODIGO = '" + cCliente + "' " 
    cQuery += "   AND ADY_LOJA   = '" + cLoja    + "' " 
    cQuery += "   AND ADY_XMODAL = '" + cNivel   + "' " 
    cQuery += "   AND ADY_STATUS > 'H'
    cQuery += "   AND D_E_L_E_T_ = ' ' " 
    cQuery += "   ORDER BY RECNO " 

    DbUseArea(.T., "TOPCONN", TcGenQry(NIL, NIL, cQuery), cTMP, .T., .F.)
	If (cTMP)->(Eof())		
        (cTMP)->(DbCloseArea())
        Return ""
    EndIf

Return cTMP

User Function GCIIA11M(cContra, cSituac, lTelaCtr)
    Local cCliente   := Subs(cContra, 4, 6)  
    Local cLoja      := "00"
    Local lIntCRM	 := IsInCallStack("U_TGCVA032")
    Local lRollBack  := IsInCallStack("U_TGCVA058")
    
    Default lTelaCtr := .F.

    If !GetMV("TI_GCIIA11",,.T.) 
        Return
    EndIf

    If cSituac == "05" .and. ! lTelaCtr
        Return
    EndIf

    If lIntCRM
        U_GCIIA11I(cCliente, cLoja, cContra, cSituac)
    ElseIf lRollBack
        U_GCIIA11K(cContra, cSituac)
    Else
        oFoto:= Tgcvxc20():New(cCliente, cLoja, cSituac)
        oFoto:lModelo := .T.
        oFoto:Load()
        oFoto:LoadModelo()
        oFoto:Reprecifica()
        oFoto:ChkManual()
        oFoto:ChkAlt()
        oFoto:Save()
        cMsgRet:= oFoto:cMsgRet

        oFoto:Destroy() 
        FreeObj(oFoto)
        oFoto := NIL 
    EndIf

Return

User Function GCIIA11K(cContra, cSituac)
	Local cMsgRet := ""
    Local oFoto   
    Local cCliente   := Subs(cContra, 4, 6)  
    Local cLoja      := "00"
    Local aPropos    := U_GV68GtPrp() 
    Local aProFoto   := {}
    Local ny         := 1
    Local nx         := 1
    Local nPosPropos := 0
    Local cProposta  := ""
    Local cDtSitu    := ""
    Local lChkAlt    := .F.
    Local lRollBack  := IsInCallStack("U_TGCVA058")
    
    
    If lRollBack 
        For ny:= 1 to Len(aPropos)
            For nx := 1 to len(aPropos[ny, 2])
                cProposta   := aPropos[ny, 2, nx, 1]
                ADY->(dbSetOrder(1))
                ADY->(dbSeek(FWxFilial("ADY") + cProposta ))
                cDtSitu     := Dtos(ADY->ADY_DTUPL)
                
                If ! u_GcxF2Fot(ADY->ADY_XCODAG, ADY->ADY_XMODAL) // essa função posiciona a PT6
                    Loop  
                EndIf 

                nPosPropos := aScan(aProFoto,{|x| AllTrim(x[1]) == cProposta})
                
                If nPosPropos == 0
                    aadd(aProFoto, {cProposta, cDtSitu} )
                EndIf
                lChkAlt := .t. 
            Next 
        Next 
        aSort(aProFoto,,, { |x,y| x[2] > y[2]} )

    EndIf 

    If cSituac == "05"
        Return 
    EndIf 

        
    oFoto:= Tgcvxc20():New(cCliente, cLoja, cSituac)
    oFoto:Load(aProFoto)
    oFoto:lModelo := .T.
    If lChkAlt
        oFoto:RollBack(aProFoto)
        oFoto:ChkAlt()
    EndIf
    oFoto:Save()
	cMsgRet:= oFoto:cMsgRet

	oFoto:Destroy() 
	FreeObj(oFoto)
	oFoto := NIL 

Return cMsgRet


User Function IICalcMet(nQuant, nBase, cFormula, nOfertas, nPDesc, nPOver, nLimFator, nLimAgru, nVlrUni1, nVlrUni2) 
    Local nPDCalc  := 0
    Local cPrcBase := ""
    Local cQtd     := ""
    Local nVlrMet  := 0
    Local nVlrUniF := 0

    Default cFormula  := ""
    Default nQuatn    := 1
    Default nOfertas  := 1
    Default nPDesc    := 0
    Default nPOver    := 0 
    Default nLimFator := 0
    Default nLimAgru  := 0

    Default nVlrUni1  := 0 //valor unitario sem desconto e sem over
    Default nVlrUni2  := 0 //valor unitario com desconto e sem over
    

    If nOfertas > 0 
        nPDCalc := nPDesc * (nOfertas - 1)
        If nLimFator > 0 .and. nLimFator <= nPDCalc
            nPDCalc := nLimFator
        ElseIf nLimAgru > 0 .and. nLimAgru <= nPDCalc
            nPDCalc := nLimAgru
        EndIf 
    EndIf 

    If ! Empty(cFormula)
        cFormula  := Lower(AllTrim(cFormula))
        cPrcBase  := AllTrim(Str(nBase))
        cQtd      := AllTrim(str(nQuant))
        cFormula  := StrTran(StrTran(cFormula, "$", cPrcBase), "x", cQtd)
        nVlrUni1  := &(cFormula) 

        If Valtype(nVlrUni1) <> "N"
            nVlrUnit := 0
        EndIf  
        nVlrUni1 := Round(nVlrUni1, 2)
    Else 
        nVlrUni1 := nBase
    EndIf 

    nVlrUni2 := Round(nVlrUni1 -  (nVlrUni1 * nPDCalc / 100), 2)  // aplica o desconto
    nVlrUniF := Round(nVlrUni2 +  (nVlrUni2 * nPover  / 100), 2)  // aplica o over

    nVlrMet := nVlrUniF * nQuant

Return nVlrMet 


User Function IICalcBase(nQuant, nVlrMet, cFormula, nOfertas, nPDesc, nPOver, nLimFator, nLimAgru, nVlrUni1, nVlrUni2) 
    Local nPDCalc  := 0
    Local cPrcBase := ""
    Local cQtd     := ""
    Local nVlrBase := 0

    Default cFormula  := ""
    Default nQuatn    := 1
    Default nOfertas  := 1
    Default nPDesc    := 0
    Default nPOver    := 0 
    Default nLimFator := 0
    Default nLimAgru  := 0

    Default nVlrUni1  := 0 //Valor Unitario com desconto sem over
    Default nVlrUni2  := 0 //valor unitario com desconto e sem over
    

    If nOfertas > 0 
        nPDCalc := nPDesc * (nOfertas - 1)
        If nLimFator > 0 .and. nLimFator <= nPDCalc
            nPDCalc := nLimFator
        ElseIf nLimAgru > 0 .and. nLimAgru <= nPDCalc
            nPDCalc := nLimAgru
        EndIf 
    EndIf 

    nVlrUni1 := nVlrMet - (nVlrMet * (nPOver / 100) / (1 + (nPOver / 100)))  //Valor Unitario com desconto sem over
    nVlrUni2 := nVlrUni1 / (1 - (nPDCalc / 100))

    If ! Empty(cFormula)
        cFormula  := Lower(AllTrim(cFormula))
        cPrcBase  := AllTrim(Str(nVlrUni2))
        cQtd      := AllTrim(str(nQuant))
        cFormula  := StrTran(StrTran(cFormula, "$", "1"), "x", cQtd)
        cFormula  := cPrcBase + " / (" + cFormula + ")"
        nVlrBase  := &(cFormula) 

        If Valtype(nVlrBase) <> "N"
            nVlrBase := 0
        EndIf  
        nVlrBase := Round(nVlrBase, 2)
    Else 
        nVlrBase := Round(nVlrUni2, 2)
    EndIf 


Return nVlrBase

User Function IIVlrFim(nQtde, nVlrMet, nVlrServ)
    Local nVlrFim  := 0
    Local nVlrUSer := 0

    nVlrUSer := Round(nVlrServ / nQtde , 2)

    nVlrFim := nVlrMet + (nVlrUSer * nQtde )

Return nVlrFim
