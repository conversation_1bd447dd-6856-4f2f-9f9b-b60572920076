#include "totvs.ch"

#xtranslate SomaAM(<cAM>, <nQ>) => eval({|x, y| cAM:= x, aA := Array(y), aeval(aA, {|| cAM := If(Right(cAM, 2) == "12", soma1(Left(cAM, 4)) + "01", soma1(cAM))}), cAM}, <cAM>, <nQ>)
#xtranslate TiraAM(<cAM>, <nQ>) => eval({|x, y| cAM:= x, aA := Array(y), aeval(aA, {|| cAM := If(Right(cAM, 2) == "01", Tira1(Left(cAM, 4)) + "12", Tira1(cAM))}), cAM}, <cAM>, <nQ>)
#xtranslate CmptoAM( <v1> )     => Right(<v1>, 4) + Left(<v1>, 2) 
#xtranslate AMtoCmp( <v1> )     => Right(<v1>, 2) + "/" + Left(<v1>, 4) 


User Function Tgcvxc21
Return

//tratamento PQB
/*
Classe de tratamneto do PQB

TIADMVIN-3138

*/ 

Class Tgcvxc21

	Data oFoto     as object
    Data cCliente  as String
    Data cLoja     as String
    Data cContrato as String
    Data cRevisa   as String
    Data nMetrica  as Numeric
    
    Data nAt       as Numeric

    Data aDados    as Array 
    Data aEstru    as Array 
    Data aRecno    as Array
    Data cTipoFoto as String
    Data cChaveTP  as String
    Data oFotoDet
    Data oMetrica
    Data oExcecao
    Data oUltCli
    Data lNewCicle  as Boolean 
    Data lMulta     as Boolean 
    Data lReprovado as Boolean 
    Data lReaju     as Boolean
    Data lMudouFx   as Boolean
    Data lCNBDif    as Boolean 
    Data cAniver    as String
    Data nQtdFaixas as Numeric
    Data lIncAuditaLS  as Boolean 
       
        
	Method New(oFoto, cTipoFoto, cChaveTP)
    Method Load()
    Method Default()
    Method Save()
    Method AtuMetrica()
    Method CriaCiclo(cProposta)
    Method NovoCiclo()
    Method SetFaixa()
    Method CanReaju()
    Method AtuNiver(cMAAniv)
    Method SaveLine(cAlias, lInc)
    Method Destroy()
    Method GetLine()
    Method NewLine(cAlias)
    Method PutLine()
    Method Pos(cCampo)
    Method GetValue(cCampo)
    Method PutValue(cCampo, uConteudo)
    Method Find(cKeyName, cChave)
       
   
EndClass

Method New(oFoto, cTipoFoto, cChaveTP) Class Tgcvxc21
    ::oFoto      := oFoto
    ::cCliente   := oFoto:cCliente
    ::cLoja      := oFoto:cLoja
    ::cContrato  := oFoto:cContrato
    ::cRevisa    := oFoto:cRevisa
    ::nMetrica   := 0
    
    ::nAt        := 0

    ::aDados     := {}
    ::aEstru     := PQB->(DbStruct())
    ::aRecno     := {}
    ::cTipoFoto  := cTipoFoto
    ::cChaveTP   := cChaveTP
    ::lNewCicle  := .f.
    ::lMulta     := .f.
    ::lReaju     := .f.
    ::lMudouFx   := .f.
    ::cAniver    := ""
    ::lReprovado := .F.
    ::lCNBDif    := .F.
    ::nQtdFaixas := 0
    ::lIncAuditaLS  := .F.
    
    ::oFotoDet := Tgcvxc22():New(Self)
    ::oMetrica := Tgcvxc23():New(Self)
    ::oExcecao := Tgcvxc24():New(Self)
    ::oUltCli  := Tgcvxc26():New(Self)
    
Return

Method Load() Class Tgcvxc21
    Local aArea := PQB->(GetArea())
    
    PQB->(DbSetOrder(2)) //PQB_FILIAL+PQB_CODCLI+PQB_LOJA+PQB_FOTOGR+PQB_CHAVFO+PQB_STATUS
	If ::cTipoFoto == "2"
        If ! PQB->(DbSeek(FWxFilial("PQB") + ::cCliente + ::cLoja + ::cTipoFoto + ::cChaveTP + "A"))
            If ! PQB->(DbSeek(FWxFilial("PQB") + ::cCliente + ::cLoja + " " + Space(15) + "A"))
                RestArea(aArea)
                Return 
            EndIf 
        EndIf 
    Else 
        If ! PQB->(DbSeek(FWxFilial("PQB") + ::cCliente + ::cLoja + ::cTipoFoto + ::cChaveTP + "A"))
            RestArea(aArea)
            Return 
        EndIf 
    EndIF 
    ::cAniver  := PQB->PQB_ANIVER
    ::nMetrica := PQB->PQB_MTRBAS

    /*
    If ! Empty(PQB->PQB_MTRBAS)
		::nMetrica := PQB->PQB_MTRBAS
	Else
	    ::nMetrica := PQB->PQB_MTRINF
    EndIf 
    */
    Self:PutLine("PQB")
	
    RestArea(aArea)

    ::oExcecao:Load()
    ::oFotoDet:Load()
    ::oMetrica:Load()
    
    
    Self:Default()   // carrega campos em branco como se fosse do novo intera
    If ! ::lNewCicle
        ::oFotoDet:AtuFaixa()
    EndIf 
    
Return 

// carrega campos em branco como se fosse do novo intera

Method Default() Class Tgcvxc21

    If Empty(Self:GetValue("PQB_FAIXA")) .and. ! ::lNewCicle
        Self:PutValue("PQB_FAIXA", StrZero(::oFotoDet:nFaixa, 2))
    Endif
    If Empty(Self:GetValue("PQB_FOTOGR"))
        Self:PutValue("PQB_FOTOGR", "2"          )
        Self:PutValue("PQB_CHAVFO", Padr("NOVO INTERA", 15))
    EndIf 
    If Empty(Self:GetValue("PQB_CODAGR"))
        Self:PutValue("PQB_CODAGR", "000112")
        Self:PutValue("PQB_CODNIV", "0304"  ) 
    EndIf 

Return 


Method Save(lRevisa) Class Tgcvxc21
    Local aArea   := PQB->(GetArea())
    Local nx      := 0
    Local nAnt    := ::nAt
    Local nRecno  := 0

    PQB->(DbSetOrder(1)) //PQB_FILIAL+PQB_CODCLI+PQB_LOJA+PQB_STATUS

    For nx:= 1 to Len(::aDados)
        ::nAt := nx
        nRecno := ::aRecno[nx]
        
        If Empty(nRecno)
            Self:SaveLine("PQB", .T.)
        Else 
            PQB->(DbGoto(nRecno))
            Self:SaveLine("PQB", .F.)
        EndIf 

    Next 
    ::nAt := nAnt

    If ! ::lNewCicle .or. ::oFoto:lIntegra .or. ::oFotoDet:lRevNew .or. ::oFoto:lAjusteFin
        ::oFotoDet:Save(lRevisa)
    EndIf
    ::oMetrica:Save()
    ::oExcecao:Save()
    If ::oFoto:lIntegra .Or. ::oFoto:lRollBack
        ::oUltCli:Save()
    EndIf 

    RestArea(aArea)
Return 


Method AtuMetrica() CLASS Tgcvxc21
    Local oMetrica   := ::oMetrica
    Local aDadosPQA  := oMetrica:aDados
    Local nx         := 0
    Local cAniver    := ::cAniver
    Local cTipo      := ""
    Local cCGCAGR    := ""
    
    Local nMtrDec    := 0
    Local nMtrInf    := 0
    Local nTotInf    := 0
    Local nTotDec    := 0
    Local nTotBas    := 0
    Local aClientes  := {} 
    Local np         := 0
    Local cCompro    := ""
    Local cOriAtu    := ""
    Local dDtMDec    := ""


    If Self:oMetrica:IsComprovado()
       Self:PutValue("PQB_FXMULT", "")
    EndIf 


    For nx := 1 to len(aDadosPQA)
        oMetrica:nAt := nx
        
        If oMetrica:GetValue("PQA_STATUS") != "A"
            Loop
        EndIf 
        If oMetrica:GetValue("PQA_ANIVER") != cAniver
            Loop
        EndIf 

        If oMetrica:Self:GetValue("PQA_APRREP") == "2" //reprovado
            Loop
        EndIf

        cTipo     := oMetrica:GetValue("PQA_TPCNPJ")
        cCGCAGR   := Alltrim(oMetrica:GetValue("PQA_CGCAGR"))
        nMtrInf   := oMetrica:GetValue("PQA_MTRINF")
        nMtrDec   := oMetrica:GetValue("PQA_MTRDEC")
        cCompro   := oMetrica:GetValue("PQA_COMPRO") 
        cOriAtu   := oMetrica:GetValue("PQA_ORIATU") 
        dDtMDec   := oMetrica:GetValue("PQA_DTMDEC")
        
        If cTipo == "1"
            cCGCAGR := Left(cCGCAGR, 8)
        EndIf 
        np := Ascan(aClientes, { |x| Alltrim(x[1]) == cCGCAGR })
        If Empty(np)
            If Empty(cCompro) 
                If cOriAtu == "4"
                    cCompro := "4"  // não se aplica
                ElseIf cOriAtu == "3"
                    cCompro := "3" // multado
                ElseIf Empty(dDtMDec)
                    cCompro := "1" // pendente
                ElseIf cOriAtu $ "1,2"
                    cCompro := "2" // comprovado
                EndIf 
            EndIf 

            aadd(aClientes, {cCGCAGR, 0, 0, cCompro})
            np := len(aClientes)
        EndIf 

        nTotInf += nMtrInf
        nTotDec += nMtrDec
        aClientes[np, 2] += nMtrInf
        aClientes[np, 3] += nMtrDec
    Next 

    nTotBas := 0
    For nx := 1 to len(aClientes)
        If aClientes[nx, 4] == "2"  //1=Pendente;2=Comprovado;3=Multado
            nTotBas   += aClientes[nx, 3]
        Else 
            nTotBas   += aClientes[nx, 2]
        EndIf  
    Next 

    If Empty(Self:GetValue("PQB_MTRINI"))
        Self:PutValue("PQB_MTRINI", nTotInf)
    EndIf 

    Self:PutValue("PQB_MTRINF", nTotInf)
    Self:PutValue("PQB_MTRDEC", nTotDec)
    Self:PutValue("PQB_MTRBAS", nTotBas)
    ::nMetrica := nTotBas
    
Return 

Method CriaCiclo(cProposta) Class Tgcvxc21
    Local cKeyName   := "PQB_STATUS+PQB_FOTOGR+PQB_CHAVFO"
    Local cChave     := "A" + ::cTipoFoto + ::cChaveTP
    Local cAniver    := ::cAniver
    Local cContrato  := ::cContrato
    Local cRevisa    := ::cRevisa
    Local cProxCiclo := ""
    Local dDtMigr    := ctod("")
    Local nMtrInf    := 0
    Local nMtrIni    := 0
    Local lChk90Dias := SuperGetMV("TI_IICHK90",, .f.)  // PARAMETRO QUE INDICA A VERIFICAÇÃO DOS 90 DIAS
    Local cAMBase    := Left(Dtos(dDataBase), 6) 

    If lChk90Dias 
        cProxCiclo := SomaAM(cAniver, 3)
    Else 
        cProxCiclo    := SomaAM(::cAniver, 12)
    EndIf

    If Self:Find(cKeyName, cChave)
        If lChk90Dias .AND. ::oMetrica:PrimeiroIli(cProposta) .and. ! ::oMetrica:IsComprovado()
            cProxCiclo := SomaAM(cAMBase, 3)
            Self:PutValue("PQB_PROXCI", cProxCiclo )
        EndIf 
		Return  
    EndIf
   
    Self:NewLine("PQB")
    Self:PutValue("PQB_CODCLI", ::cCliente )
    Self:PutValue("PQB_LOJA"  , ::cLoja    )
    Self:PutValue("PQB_STATUS", "A"        )
    Self:PutValue("PQB_DTMIGR", dDatabase  ) 
    Self:PutValue("PQB_ANIVER", cAniver    ) 
    Self:PutValue("PQB_CONTRA", cContrato  )
    Self:PutValue("PQB_REVORI", cRevisa    )   
    Self:PutValue("PQB_PROXCI", cProxCiclo )
    Self:PutValue("PQB_ENVIAD", "2"		   )
    Self:PutValue("PQB_DTMIGR", dDtMigr    )
    Self:PutValue("PQB_MTRINF", nMtrInf    )
    Self:PutValue("PQB_MTRINI", nMtrIni    )
    Self:PutValue("PQB_MTRDEC", 0          )
    Self:PutValue("PQB_MTRBAS", 0          )
    Self:PutValue("PQB_FOTOGR", ::cTipoFoto)
    Self:PutValue("PQB_CHAVFO", ::cChaveTP )
    Self:PutValue("PQB_CODAGR", ::oFoto:cCodAgr)
    Self:PutValue("PQB_CODNIV", ::oFoto:cXModal)

Return 




Method NovoCiclo() Class Tgcvxc21
    Local cKeyName   := "PQB_STATUS+PQB_FOTOGR+PQB_CHAVFO"
    Local cChave     := "A" + ::cTipoFoto + ::cChaveTP
    Local cAniver    := SomaAM(::cAniver, 12)
    Local cContrato  := ::cContrato
    Local cRevisa    := ::cRevisa
    Local dDtMigr    := ctod("")
    Local nMtrInf    := 0
    Local nMtrIni    := 0
    Local nMtrDec    := 0
    Local cMesCiclo  := Right(TiraAM(::cAniver, 1), 2)
    Local cCicloAtu  := ""
    Local cCodAgr    := ""
    Local cXModal    := ""
    Local cFaixa     := ""

    If ! Self:Find(cKeyName, cChave)
		Return  
    EndIf

    If StrZero(Month(dDataBase), 2) != cMesCiclo
        Return 
    EndIf  

    dDtMigr   := Self:GetValue("PQB_DTMIGR")
    nMtrInf   := Self:GetValue("PQB_MTRINF")
    nMtrIni   := Self:GetValue("PQB_MTRINI")
    nMtrDec   := Self:GetValue("PQB_MTRDEC")
    cFaixa    := Self:GetValue("PQB_FAIXA" )
    cCicloAtu := Self:GetValue("PQB_PROXCI")
    cCodAgr   := Self:GetValue("PQB_CODAGR")
    cXModal   := Self:GetValue("PQB_CODNIV")
    If nMtrDec > 0
        nMtrInf := nMtrDec
        nMtrIni := nMtrDec
    EndIf 
    If cCicloAtu < cAniver
        cCicloAtu := cAniver            
    EndIf 

    Self:PutValue("PQB_STATUS", "I"       )  

    Self:NewLine("PQB")
    Self:PutValue("PQB_CODCLI", ::cCliente )
    Self:PutValue("PQB_LOJA"  , ::cLoja    )
    Self:PutValue("PQB_STATUS", "A"        )
    Self:PutValue("PQB_DTMIGR", dDatabase  ) 
    Self:PutValue("PQB_ANIVER", cAniver    ) 
    Self:PutValue("PQB_CONTRA", cContrato  )
    Self:PutValue("PQB_REVORI", cRevisa    )   
    Self:PutValue("PQB_ENVIAD", "2"		   )
    Self:PutValue("PQB_DTMIGR", dDtMigr    )
    Self:PutValue("PQB_MTRINF", nMtrInf    )
    Self:PutValue("PQB_MTRINI", nMtrIni    )
    Self:PutValue("PQB_MTRDEC", 0          )
    Self:PutValue("PQB_MTRBAS", 0          )
    Self:PutValue("PQB_FAIXA" , cFaixa     )
    Self:PutValue("PQB_FOTOGR", ::cTipoFoto)
    Self:PutValue("PQB_CHAVFO", ::cChaveTP )
    Self:PutValue("PQB_PROXCI", cCicloAtu  )
    Self:PutValue("PQB_CODAGR", cCodAgr    )
    Self:PutValue("PQB_CODNIV", cXModal    )
   
    ::cAniver   := cAniver
    ::lNewCicle := .t. 

Return 


// seta a faixa utilizado na Atufaixa da pqc
Method SetFaixa() Class Tgcvxc21
    Local cFaixa := ""
    Local nFaixa := 0
    
    If ::oExcecao:lExcec15
        nFaixa := ::oExcecao:nFxExcec15
        cFaixa := StrZero(nFaixa, 2)
        Self:PutValue("PQB_FAIXA", cFaixa)
        Return 
    EndIf 

    If ::lMulta
        Return 
    EndIf 

    If ! ::lNewCicle
        nFaixa := Val(Self:GetValue("PQB_FXMULT"))
        If  Empty(nFaixa)
            nFaixa := ::oFotoDet:CalcFxMetri()
        EndIf 
        cFaixa := StrZero(nFaixa, 2)
        Self:PutValue("PQB_FAIXA", cFaixa)
    EndIf 

Return  

MetHod CanReaju(cAMAnive) Class Tgcvxc21
    Local nMesAni := Val(Right(cAMAnive, 2))
    Local nMesObj := Val(Right(::cAniver, 2))

    If nMesAni != nMesObj
        Return .f.
    EndIf 

    If ::cAniver != cAMAnive  
        ::oFoto:cMsgRet := "Não foi aberto o novo ciclo!"
        Return .f. 
    EndIf 
    /*
    If ! ::lNewCicle
        ::oFoto:cMsgRet := "Não foi aberto o novo ciclo!"
        Return .f. 
    EndIf 
    */
    If ::oFotoDet:GetValue("PQC_ANIVER") == cAMAnive
        ::oFoto:cMsgRet := "OK - Processado anteriormente!"
        Return .f. 
    EndIf 

Return .t.

Method AtuNiver(cMAAniv) Class Tgcvxc21
    Local cAMAniv    := CmpToAM(cMAAniv)
    Local cProxCiclo := SomaAM(cAMAniv, 12)

    Self:PutValue("PQB_ANIVER", cAMAniv)
    Self:PutValue("PQB_PROXCI", cProxCiclo )
    
    ::oMetrica:AtuNiver(cMAAniv)
    ::oFotoDet:AtuNiver(cMAAniv)

Return 

Method Destroy() Class Tgcvxc21

    aSize(::aDados, 0)
    aSize(::aEstru, 0)
    aSize(::aRecno, 0)

    FreeObj(::oMetrica)
    FreeObj(::oFotoDet)
    FreeObj(::oExcecao)
    
    ::oMetrica := NIL 
    ::oFotoDet := NIL  
    ::oExcecao := NIL  
    
Return


Method SaveLine(cAlias, lInc) CLASS Tgcvxc21
    Local nx 
    Default lInc := .t. 

    (cAlias)->(RecLock(cAlias, lInc))
	For nx := 1 To len(::aEstru)
        uConteudo := ::aDados[::nAt, nx]
        (cAlias)->(FieldPut(nx, uConteudo))
	Next 
    (cAlias)->(MsUnlock())

Return

Method GetLine() CLASS Tgcvxc21
    Local nx 
    Local aDadosRec := {}

	For nx := 1 To len(::aEstru)
        uConteudo := ::aDados[::nAt, nx]
	    aadd(aDadosRec , uConteudo)
	Next 
    aadd(::aDados, aClone(aDadosRec))

Return aClone(aDadosRec)

Method NewLine(cAlias) Class Tgcvxc21
    Local nx 
    Local aDadosRec := {}
    Local cTipo 

	For nx := 1 To len(::aEstru)
        uConteudo := (cAlias)->(FieldGet(nx))
        cTipo     := ValType(uConteudo) 
        If cTipo == "C"
            uConteudo := Space(len(uConteudo))
        ElseIf cTipo == "D"
            uConteudo := ctod("")
        ElseIf cTipo == "N"
            uConteudo := 0
        ElseIf cTipo == "L"
            uConteudo := .F.
        ElseIf cTipo == "M"
            uConteudo := ""
        EndIf 
	    aadd(aDadosRec , uConteudo)
	Next 
    aadd(::aDados, aClone(aDadosRec))
    aadd(::aRecno, 0)
    ::nAt := Len(::aDados)


Return 

 



Method PutLine(cAlias) CLASS Tgcvxc21
    Local nx 
    Local aDadosRec := {}

	For nx := 1 To len(::aEstru)
        uConteudo := (cAlias)->(FieldGet(nx))
	    aadd(aDadosRec , uConteudo)
	Next 
    aadd(::aDados, aClone(aDadosRec))
    aadd(::aRecno, (cAlias)->(Recno()))
    ::nAt++

Return 

Method Pos(cCampo) CLASS Tgcvxc21
    Local np := 0

    np := Ascan(::aEstru, {|x| Alltrim(x[1]) == Alltrim(cCampo)})

Return np

Method GetValue(cCampo) CLASS Tgcvxc21
    Local np :=0
	Local uRet := NIL

    If Empty(::aDados)
        Return uRet
    EndIf 

    np:= Self:Pos(cCampo)
    If ! Empty(np) 
        uRet := ::aDados[::nAt, np]
    EndIf 
    
Return uRet

Method PutValue(cCampo, uConteudo) CLASS Tgcvxc21
	Local np :=0

    np:= Self:Pos(cCampo)
    If ! Empty(np)
	    ::aDados[::nAt, np] := uConteudo
	EndIf

Return

Method Find(cKeyName, cChave) CLASS Tgcvxc21
    Local nx      := 0 
    Local ny      := 0
    Local aCampos := {}
    Local cTemp   := "" 
    Local lRet    := .F. 
    
    aCampos   := Separa(cKeyName, "+")
    
    ::nAt := 0
    For nx := 1 to len(::aDados)
        ::nAt := nx
        cTemp   := ""
        For ny := 1 to len(aCampos)
            cTemp += Self:GetValue(aCampos[ny])
        Next 
        If cTemp == cChave 
            lRet := .t.
            Exit 
        EndIf 
    Next 

Return lRet


