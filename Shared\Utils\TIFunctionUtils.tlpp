#INCLUDE 'TOTVS.CH'
#Include "tlpp-core.th"

namespace Shared.Utils

User Function TIEmailEnv(cProdEmail, cTestEmail)
Local oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()
Local cEmail := oEnvControl:GetEmailByEnv(cProdEmail, cTestEmail)
FreeObj(oEnvControl)
oEnvControl := Nil
Return cEmail

User Function TIEnvMI()
Local oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()
Local lEnvMI := oEnvControl:IsMiEnvironment()
FreeObj(oEnvControl)
oEnvControl := Nil
Return lEnvMI

/*/{Protheus.doc} TIEnv
Função que retorna informações detalhadas do ambiente atual em formato JSON

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
@return Object, JSON com informações do ambiente
@example
/*/
User Function TIEnv(cType)

Local oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()
Local oJson := oEnvControl:GetInfoEnvironment()

Do Case
    Case cType == "ENVIRONMENT"
        Return oJson:GetJsonObject("environment")
    case cType == "SESSION"
        Return oJson:GetJsonObject("session")
    case cType == "TECHNICAL"
        Return oJson:GetJsonObject("technical")
    case cType == "PARAMETERS"
        Return oJson:GetJsonObject("parameters")
    Otherwise
        Return oJson
EndCase

// Libera objeto
If oEnvControl != Nil
    FreeObj(oEnvControl)
    oEnvControl := Nil
EndIf

Return oJson


User Function TIEnvType(lLocal As Logical)
Local oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()
Local cEnvType := oEnvControl:GetEnvironmentType()

Default lLocal := .F.

If lLocal
    cEnvType += IIF(oEnvControl:IsMiEnvironment(), "-MI", "-BR")
EndIf

FreeObj(oEnvControl)
oEnvControl := Nil
Return cEnvType



User Function TIVerifyEnv(cType,cLocal)
Local oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()
Local lReturn := .F.

Default cLocal := "BR"
Default cType := "PROD"

Do Case
    Case cType == "PROD"
        lReturn := oEnvControl:IsProduction()
    case cType == "PRE"
        lReturn := oEnvControl:IsPreProduction()
    case cType == "STG"
        lReturn := oEnvControl:IsStaging()
    case cType == "DEV"
        lReturn := oEnvControl:IsDevelopment()
EndCase

If lReturn .And. cLocal == "MI"
    lReturn := oEnvControl:IsMiEnvironment()
EndIf

// Libera objeto
If oEnvControl != Nil
    FreeObj(oEnvControl)
    oEnvControl := Nil
EndIf

Return lReturn


