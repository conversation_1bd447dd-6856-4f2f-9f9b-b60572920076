#Include "tlpp-core.th"
#INCLUDE 'TOTVS.CH'

/*/{Protheus.doc} TesteEnvironmentControl
{Protheus.doc} U_TSTENVCTRL
Função principal para executar todos os testes da classe EnvironmentControl
Execute: U_TSTENVCTRL()

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENVCTRL()

Local nOpcao := 0
Local aTestes := {}

// Menu de testes disponíveis
aadd(aTestes, "1 - Verificação de Ambiente")
aadd(aTestes, "2 - Configuração Personalizada")
aadd(aTestes, "3 - Métodos Auxiliares (Email/Parâmetros)")
aadd(aTestes, "4 - Compatibilidade com Código Existente")
aadd(aTestes, "5 - Performance e Múltiplas Instâncias")
aadd(aTestes, "6 - Cenários de Erro")
aadd(aTestes, "7 - Verificação tipo de ambiente")
aadd(aTestes, "8 - Simulação da Função GCVWFVTEX Refatorada")
aadd(aTestes, "9 - Informação do Ambiente")
aadd(aTestes, "10 - Integração TIEnv x EnvironmentControl")
aadd(aTestes, "11 - Selecão de Email")
aadd(aTestes, "12 - Verificação se é ambiente MI")
aadd(aTestes, "13 - Completo (Todos os testes)")
aadd(aTestes, "0 - Sair")

While .T.
    nOpcao := Aviso("Teste EnvironmentControl", "Escolha o teste a executar:", aTestes, 3)

    Do Case
        Case nOpcao == 1
            U_TSTENV01() // Verificação de Ambiente
        Case nOpcao == 2
            U_TSTENV02() // Configuração Personalizada
        Case nOpcao == 3
            U_TSTENV03() // Métodos Auxiliares
        Case nOpcao == 4
            U_TSTENV04() // Compatibilidade
        Case nOpcao == 5
            U_TSTENV05() // Performance
        Case nOpcao == 6
            U_TSTENV06() // Cenários de Erro
        Case nOpcao == 7
            U_TSTENV13() // Verificação tipo de ambiente
        Case nOpcao == 8
            U_TSTENV08() // Simulação da Função GCVWFVTEX
        Case nOpcao == 9
            U_TSTENV09() // Informação do Ambiente
        Case nOpcao == 10
            U_TSTENV10() // Integração TIEnv x EnvironmentControl
        Case nOpcao == 11
            U_TSTENV11() // Selecão de Email
        Case nOpcao == 12
            U_TSTENV12() // Verificação se é ambiente MI
        Case nOpcao == 13
            U_TSTENV07() // Executa todos os testes
        Case nOpcao == 14 .Or. nOpcao == 0
            Exit
    EndCase
EndDo

Return

//------------------------------------------------------------------
/*/{Protheus.doc} U_TSTENV01
Teste Básico - Verificação de Ambiente
Testa os Métodos Básicos de Verificação de ambiente

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENV01()

Local oEnvControl := Nil
Local cMsg := ""
Local cCurrentEnv := ""
Local cEnvType := ""

ConOut(Replicate("=", 60))
ConOut("TESTE 1: Verificação básica de ambiente")
ConOut(Replicate("=", 60))

Try
    // Instancia a classe
    oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()

    // Coleta informações básicas
    cCurrentEnv := oEnvControl:GetCurrentEnv()
    cEnvType := oEnvControl:GetEnvironmentType()

    // Monta mensagem de resultado
    cMsg := "Ambiente ATUAL: " + cCurrentEnv + CRLF
    cMsg += "TIPO: " + cEnvType + CRLF + CRLF
    cMsg += "Verificações:" + CRLF
    cMsg += "É Produção: " + IIF(oEnvControl:IsProduction(), "Sim", "Não") + CRLF
    cMsg += "É Homologação: " + IIF(oEnvControl:IsStaging(), "Sim", "Não") + CRLF
    cMsg += "É Desenvolvimento: " + IIF(oEnvControl:IsDevelopment(), "Sim", "Não") + CRLF

    // Log no console
    ConOut("Ambiente atual: " + cCurrentEnv)
    ConOut("Tipo: " + cEnvType)
    ConOut("É Produção: " + IIF(oEnvControl:IsProduction(), "Sim", "Não"))
    ConOut("É Homologação: " + IIF(oEnvControl:IsStaging(), "Sim", "Não"))
    ConOut("É Desenvolvimento: " + IIF(oEnvControl:IsDevelopment(), "Sim", "Não"))

    // Exibe resultado
    MsgInfo(cMsg, "Teste 1 - Verificação básica")

Catch oError
    cMsg := "ERRO no Teste 1: " + oError:Description
    ConOut(cMsg)
    MsgAlert(cMsg, "Erro")

Finally
    // Libera objeto
    If oEnvControl != Nil
        FreeObj(oEnvControl)
        oEnvControl := Nil
    EndIf

EndTry

ConOut("TESTE 1 FINALIZADO")
ConOut(Replicate("-", 60))

Return

//------------------------------------------------------------------
/*/{Protheus.doc} U_TSTENV02
Teste de Configuração Personalizada
Testa a Configuração personalizada de ambientes

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENV02()

Local oEnvControl := Nil
Local cMsg := ""
Local cCurrentEnv := ""

ConOut(Replicate("=", 60))
ConOut("TESTE 2: Configuração PERSONALIZADA")
ConOut(Replicate("=", 60))

Try
    oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()
    cCurrentEnv := oEnvControl:GetCurrentEnv()

    cMsg := "Configuração INICIAL:" + CRLF
    cMsg += "Ambiente: " + cCurrentEnv + CRLF
    cMsg += "Tipo: " + oEnvControl:GetEnvironmentType() + CRLF + CRLF

    // Teste 1: Configurar como produção
    ConOut("Configurando ambiente atual como produção")
    oEnvControl:SetProductionEnv(cCurrentEnv)

    cMsg += "após CONFIGURAR COMO PRODUção:" + CRLF
    cMsg += "É Produção: " + IIF(oEnvControl:IsProduction(), "Sim", "Não") + CRLF
    cMsg += "Tipo: " + oEnvControl:GetEnvironmentType() + CRLF + CRLF

    // Teste 2: Configurar como homologação
    ConOut("Configurando ambiente atual como homologação...")
    oEnvControl:SetProductionEnv("#AMBIENTE_INEXISTENTE")
    oEnvControl:SetStagingEnv(cCurrentEnv)

    cMsg += "após configurar como homologação:" + CRLF
    cMsg += "É Homologação: " + IIF(oEnvControl:IsStaging(), "Sim", "Não") + CRLF
    cMsg += "Tipo: " + oEnvControl:GetEnvironmentType() + CRLF + CRLF

    // Teste 3: Configurar como desenvolvimento
    ConOut("Configurando ambiente atual como desenvolvimento...")
    oEnvControl:SetStagingEnv("#AMBIENTE_INEXISTENTE")
    oEnvControl:SetDevelopmentEnv(cCurrentEnv)

    cMsg += "após configurar como desenvolvimento:" + CRLF
    cMsg += "É Desenvolvimento: " + IIF(oEnvControl:IsDevelopment(), "Sim", "Não") + CRLF
    cMsg += "Tipo: " + oEnvControl:GetEnvironmentType()

    MsgInfo(cMsg, "Teste 2 - Configuração Personalizada")

Catch oError
    cMsg := "ERRO no Teste 2: " + oError:Description
    ConOut(cMsg)
    MsgAlert(cMsg, "Erro")

Finally
    If oEnvControl != Nil
        FreeObj(oEnvControl)
        oEnvControl := Nil
    EndIf

EndTry

ConOut("TESTE 2 FINALIZADO")
ConOut(Replicate("-", 60))

Return

//------------------------------------------------------------------
/*/{Protheus.doc} U_TSTENV03
Teste de Métodos Auxiliares
Testa os Métodos GetEmailByEnv e GetParameterByEnv

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENV03()

Local oEnvControl := Nil
Local cMsg := ""
Local cEmail := ""
Local cTimeout := ""
Local cUrl := ""

ConOut(Replicate("=", 60))
ConOut("TESTE 3: Métodos auxiliares")
ConOut(Replicate("=", 60))

Try
    oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()

    // Teste GetEmailByEnv
    ConOut("Testando GetEmailByEnv...")
    cEmail := oEnvControl:GetEmailByEnv(;
        "<EMAIL>",;
        "<EMAIL>";
    )

    // Teste GetParameterByEnv (Simula Parâmetros que Não existem)
    ConOut("Testando GetParameterByEnv...")
    cTimeout := oEnvControl:GetParameterByEnv(;
        "TIMEOUT_PROD_TESTE",;
        "TIMEOUT_DEV_TESTE",;
        "30";
    )

    // Teste com Parâmetro existente
    cUrl := oEnvControl:GetParameterByEnv(;
        "TI_WFVTEX",;
        "TI_032ETST",;
        "<EMAIL>";
    )

    cMsg := "Resultados dos Métodos Auxiliares:" + CRLF + CRLF
    cMsg += "Ambiente: " + oEnvControl:GetCurrentEnv() + CRLF
    cMsg += "Tipo: " + oEnvControl:GetEnvironmentType() + CRLF + CRLF
    cMsg += "GetEmailByEnv():" + CRLF
    cMsg += "Email retornado: " + cEmail + CRLF + CRLF
    cMsg += "GetParameterByEnv() - Parâmetros inexistentes:" + CRLF
    cMsg += "Timeout: " + cTimeout + " (valor padrão)" + CRLF + CRLF
    cMsg += "GetParameterByEnv() - Parâmetros existentes:" + CRLF
    cMsg += "URL/Email: " + cUrl

    ConOut("Email selecionado: " + cEmail)
    ConOut("Timeout: " + cTimeout)
    ConOut("URL: " + cUrl)

    MsgInfo(cMsg, "Teste 3 - Métodos Auxiliares")

Catch oError
    cMsg := "ERRO no Teste 3: " + oError:Description
    ConOut(cMsg)
    MsgAlert(cMsg, "Erro")

Finally
    If oEnvControl != Nil
        FreeObj(oEnvControl)
        oEnvControl := Nil
    EndIf

EndTry

ConOut("TESTE 3 FINALIZADO")
ConOut(Replicate("-", 60))

Return

//------------------------------------------------------------------
/*/{Protheus.doc} U_TSTENV04
Teste de Compatibilidade com Código Existente
Simula a migração do Código da Função GCVWFVTEX

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENV04()

Local oEnvControl := Nil
Local cMsg := ""
Local cProducaoAntigo := ""
Local lProdAntigo := .F.
Local cEmailAntigo := ""
Local cEmailNovo := ""

ConOut(Replicate("=", 60))
ConOut("TESTE 4: Compatibilidade com Código Existente")
ConOut(Replicate("=", 60))

Try
    // *** Simulação DO Código ANTIGO (GCVWFVTEX) ***
    ConOut("Executando Lógica ANTIGA...")
    cProducaoAntigo := SuperGetMV("TI_AMBPRO",,"#TOTVS12/CRM")
    lProdAntigo := IIF(GetEnvServer()$cProducaoAntigo,.T.,.F.)

    If lProdAntigo
        cEmailAntigo := SuperGetMV("TI_WFVTEX",,"<EMAIL>")
    Else
        cEmailAntigo := SuperGetMV("TI_032ETST",,"<EMAIL>")
    EndIf

    // *** Simulação DO Código NOVO (COM CLASSE) ***
    ConOut("Executando Lógica NOVA...")
    oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()

    cEmailNovo := oEnvControl:GetEmailByEnv(;
        SuperGetMV("TI_WFVTEX",,"<EMAIL>"),;
        SuperGetMV("TI_032ETST",,"<EMAIL>");
    )

    // *** COMPARAção DOS RESULTADOS ***
    cMsg := "Comparação código ANTIGO vs NOVO:" + CRLF + CRLF
    cMsg += "Método ANTIGO:" + CRLF
    cMsg += "Ambiente: " + GetEnvServer() + CRLF
    cMsg += "Parâmetro TI_AMBPRO: " + cProducaoAntigo + CRLF
    cMsg += "É Produção: " + IIF(lProdAntigo, "Sim", "Não") + CRLF
    cMsg += "Email selecionado: " + cEmailAntigo + CRLF + CRLF

    cMsg += "Método NOVO (CLASSE):" + CRLF
    cMsg += "Ambiente: " + oEnvControl:GetCurrentEnv() + CRLF
    cMsg += "Tipo: " + oEnvControl:GetEnvironmentType() + CRLF
    cMsg += "É Produção: " + IIF(oEnvControl:IsProduction(), "Sim", "Não") + CRLF
    cMsg += "Email selecionado: " + cEmailNovo + CRLF + CRLF

    cMsg += "COMPATIBILIDADE:" + CRLF
    cMsg += "Resultados iguais: " + IIF(cEmailAntigo == cEmailNovo, "Sim ?", "Não ?") + CRLF
    cMsg += "Lógica equivalente: " + IIF(lProdAntigo == oEnvControl:IsProduction(), "Sim ?", "Não ?")

    ConOut("Email Método ANTIGO: " + cEmailAntigo)
    ConOut("Email Método NOVO: " + cEmailNovo)
    ConOut("Compatibilidade: " + IIF(cEmailAntigo == cEmailNovo, "OK", "ERRO"))

    MsgInfo(cMsg, "Teste 4 - Compatibilidade")

Catch oError
    cMsg := "ERRO no Teste 4: " + oError:Description
    ConOut(cMsg)
    MsgAlert(cMsg, "Erro")

Finally
    If oEnvControl != Nil
        FreeObj(oEnvControl)
        oEnvControl := Nil
    EndIf

EndTry

ConOut("TESTE 4 FINALIZADO")
ConOut(Replicate("-", 60))

Return

//------------------------------------------------------------------
/*/{Protheus.doc} U_TSTENV05
Teste de Performance e Múltiplas Instâncias
Testa performance e uso de Múltiplas Instâncias da classe

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENV05()

Local aInstancias := {}
Local oEnvControl := Nil
Local nI := 0
Local nTempo := 0
Local cMsg := ""
Local nInicio := 0
Local nFim := 0
Local lConsistente := .T.
Local cEnvPrimeiro := ""
Local cTipoPrimeiro := ""

ConOut(Replicate("=", 60))
ConOut("TESTE 5: Performance e Múltiplas Instâncias")
ConOut(Replicate("=", 60))

Try
    // Teste de performance - criação de Múltiplas Instâncias
    ConOut("Criando 100 Instâncias da classe...")
    nInicio := Seconds()

    For nI := 1 To 100
        oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()
        aadd(aInstancias, oEnvControl)

        // Testa alguns Métodos
        oEnvControl:IsProduction()
        oEnvControl:GetCurrentEnv()
        oEnvControl:GetEnvironmentType()
    Next nI

    nFim := Seconds()
    nTempo := nFim - nInicio

    ConOut("Tempo para criar 100 Instâncias: " + AllTrim(Str(nTempo, 10, 3)) + " segundos")

    // Teste de consistência entre Instâncias
    ConOut("Testando consistência entre Instâncias...")
    lConsistente := .T.
    cEnvPrimeiro := aInstancias[1]:GetCurrentEnv()
    cTipoPrimeiro := aInstancias[1]:GetEnvironmentType()

    For nI := 2 To Len(aInstancias)
        If aInstancias[nI]:GetCurrentEnv() != cEnvPrimeiro .Or. ;
           aInstancias[nI]:GetEnvironmentType() != cTipoPrimeiro
            lConsistente := .F.
            Exit
        EndIf
    Next nI

    // Libera todas as Instâncias
    ConOut("Liberando Instâncias...")
    For nI := 1 To Len(aInstancias)
        FreeObj(aInstancias[nI])
    Next nI
    aInstancias := {}

    cMsg := "TESTE DE PERFORMANCE:" + CRLF + CRLF
    cMsg += "Instâncias criadas: 100" + CRLF
    cMsg += "Tempo total: " + AllTrim(Str(nTempo, 10, 3)) + " segundos" + CRLF
    cMsg += "Tempo Médio por inst�ncia: " + AllTrim(Str(nTempo/100, 10, 5)) + " segundos" + CRLF + CRLF
    cMsg += "CONSISTÊNCIA:" + CRLF
    cMsg += "Todas as Instâncias retornaram os mesmos valores: " + IIF(lConsistente, "Sim ?", "Não ?") + CRLF + CRLF
    cMsg += "AMBIENTE TESTADO:" + CRLF
    cMsg += "Nome: " + cEnvPrimeiro + CRLF
    cMsg += "Tipo: " + cTipoPrimeiro

    MsgInfo(cMsg, "Teste 5 - Performance")

Catch oError
    cMsg := "ERRO no Teste 5: " + oError:Description
    ConOut(cMsg)
    MsgAlert(cMsg, "Erro")

    // Libera Instâncias em caso de erro
    For nI := 1 To Len(aInstancias)
        If aInstancias[nI] != Nil
            FreeObj(aInstancias[nI])
        EndIf
    Next nI

Finally
    // Garantia adicional de limpeza
    aInstancias := {}

EndTry

ConOut("TESTE 5 FINALIZADO")
ConOut(Replicate("-", 60))

Return

//------------------------------------------------------------------
/*/{Protheus.doc} U_TSTENV06
Teste de Cenários de Erro
Testa comportamento da classe em situações de erro

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENV06()

Local oEnvControl := Nil
Local cMsg := ""
Local lErroCapturado := .F.
Local nI := 0
Local cEmailTeste := ""
Local cParamTeste := ""
Local cEnvAtual := ""
Local cTipoAtual := ""

ConOut(Replicate("=", 60))
ConOut("TESTE 6: Cenários de erro")
ConOut(Replicate("=", 60))

Try
    oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()

    cMsg := "TESTES DE ROBUSTEZ:" + CRLF + CRLF

    // Teste 1: Configuração com string vazia
    ConOut("Teste 1: Configuração com string vazia...")
    oEnvControl:SetProductionEnv("")
    cMsg += "1. Configuração com string vazia: " + IIF(oEnvControl:GetEnvironmentType() == "DEV", "OK ?", "ERRO ?") + CRLF

    // Teste 2: Configuração com Nil (Simulado com string vazia)
    ConOut("Teste 2: Configuração inválida...")
    oEnvControl:SetStagingEnv("")
    oEnvControl:SetDevelopmentEnv("")
    cMsg += "2. Configurações vazias: " + IIF(oEnvControl:GetEnvironmentType() == "DEV", "OK ?", "ERRO ?") + CRLF

    // Teste 3: Múltiplas reConfigurações
    ConOut("Teste 3: Múltiplas reConfigurações...")
    For nI := 1 To 10
        oEnvControl:SetProductionEnv("#TESTE" + AllTrim(Str(nI)))
        oEnvControl:SetStagingEnv("#TESTE" + AllTrim(Str(nI+10)))
        oEnvControl:SetDevelopmentEnv("#TESTE" + AllTrim(Str(nI+20)))
    Next nI
    cMsg += "3. Múltiplas reConfigurações: OK ?" + CRLF

    // Teste 4: Métodos auxiliares com Parâmetros inválidos
    ConOut("Teste 4: Parâmetros inválidos...")
    cEmailTeste := oEnvControl:GetEmailByEnv("", "")
    cParamTeste := oEnvControl:GetParameterByEnv("", "", "PADRAO")
    cMsg += "4. Parâmetros vazios: " + IIF(cEmailTeste == "" .And. cParamTeste == "PADRAO", "OK ?", "ERRO ?") + CRLF

    // Teste 5: Estado após Múltiplas operações
    ConOut("Teste 5: Estado após operações...")
    oEnvControl:SetProductionEnv("#TOTVS12/CRM")  // Restaura Configuração padrão
    cEnvAtual := oEnvControl:GetCurrentEnv()
    cTipoAtual := oEnvControl:GetEnvironmentType()
    cMsg += "5. Estado consistente: " + IIF(!Empty(cEnvAtual) .And. !Empty(cTipoAtual), "OK ?", "ERRO ?") + CRLF + CRLF

    cMsg += "INFORMAÇÕES FINAIS:" + CRLF
    cMsg += "Ambiente: " + cEnvAtual + CRLF
    cMsg += "Tipo: " + cTipoAtual + CRLF
    cMsg += "Classe funcionando: " + IIF(oEnvControl != Nil, "Sim ?", "Não ?")

    MsgInfo(cMsg, "Teste 6 - Cenários de Erro")

Catch oError
    lErroCapturado := .T.
    cMsg := "ERRO CAPTURADO no Teste 6:" + CRLF
    cMsg += "Descrição: " + oError:Description + CRLF
    cMsg += "Operação: " + oError:Operation + CRLF
    cMsg += "Arquivo: " + oError:FileName + CRLF
    cMsg += "Linha: " + AllTrim(Str(oError:GenCode))

    ConOut("ERRO: " + oError:Description)
    MsgAlert(cMsg, "Erro Capturado")

Finally
    If oEnvControl != Nil
        FreeObj(oEnvControl)
        oEnvControl := Nil
    EndIf

EndTry

ConOut("TESTE 6 FINALIZADO - Erro capturado: " + IIF(lErroCapturado, "Sim", "Não"))
ConOut(Replicate("-", 60))

Return

//------------------------------------------------------------------
/*/{Protheus.doc} U_TSTENV07
Executa todos os testes em sequ�ncia

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENV07()

Local nInicio := Seconds()
Local nFim := 0
Local cTempo := ""

ConOut(Replicate("=", 80))
ConOut("EXECUTANDO TODOS OS TESTES DA CLASSE ENVIRONMENTCONTROL")
ConOut(Replicate("=", 80))

// Executa todos os testes
U_TSTENV01()  // Verificação básica
U_TSTENV02()  // Configuração personalizada
U_TSTENV03()  // Métodos auxiliares
U_TSTENV04()  // Compatibilidade
U_TSTENV05()  // Performance
U_TSTENV06()  // Cenários de erro
U_TSTENV08()  // Simulação da Função GCVWFVTEX
U_TSTENV09()  // Informação do Ambiente
U_TSTENV10()  // Integração TIEnv x EnvironmentControl
U_TSTENV11()  // Teste de Selecão de Email
U_TSTENV12()  // Teste de Verificação se é ambiente MI
U_TSTENV13()  // Teste de Verificação tipo de ambiente

nFim := Seconds()
cTempo := AllTrim(Str(nFim - nInicio, 10, 2))

ConOut(Replicate("=", 80))
ConOut("TODOS OS TESTES FINALIZADOS")
ConOut("Tempo total: " + cTempo + " segundos")
ConOut(Replicate("=", 80))

MsgInfo("Todos os testes foram executados!" + CRLF + "Tempo total: " + cTempo + " segundos" + CRLF + CRLF + "Verifique o console para detalhes.", "Testes Completos")

Return

//------------------------------------------------------------------
/*/{Protheus.doc} U_TSTENV08
Simulação da Função GCVWFVTEX refatorada usando EnvironmentControl
Demonstra como seria a migração real da Função

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENV08()

Local cProposta := "PROP001"
Local cCodCli := "000001"
Local dDataWF := Date()
Local cEmail := ""

// Vari�veis da Função original
Local cPathHTML := GetMV("MV_WFDIR")
Local cFileName := ""
Local cArqHTML := "\workflow\TGCVA032-01.html"
Local cNomeCli := "CLIENTE TESTE LTDA"
Local cAssunto := ""
Local lRet := .T.

// *** NOVA IMPLEMENTAção COM A CLASSE ***
Local oEnvControl := Nil
Local lEnvEmail := SuperGetMV("TI_032VTEX",,.T.)
Local cMsg := ""

ConOut(Replicate("=", 60))
ConOut("TESTE 8: Simulação GCVWFVTEX REFATORADA")
ConOut(Replicate("=", 60))

Try
    // Instancia a classe de controle de ambiente
    oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()

    // Define email baseado no ambiente usando a classe
    Default cEmail := oEnvControl:GetEmailByEnv(;
        SuperGetMV("TI_WFVTEX",,"<EMAIL>;<EMAIL>;<EMAIL>"),;
        SuperGetMV("TI_032ETST",,"<EMAIL>");
    )

    IF lEnvEmail
        // Usa o Método da classe para determinar o email correto
        If oEnvControl:IsProduction()
            cEmail := Alltrim(SuperGetMV("TI_WFVTEX",,"<EMAIL>;<EMAIL>;<EMAIL>"))
        Else
            cEmail := Alltrim("<EMAIL>")  // Simula UsrRetMail(RetCodUsr())
        EndIf
    Endif

    // Para ambientes Não produtivos, usa email de teste
    If !oEnvControl:IsProduction()
        cEmail := SuperGetMV("TI_032ETST",,"<EMAIL>")
    EndIf

    // Monta assunto do email
    cAssunto := "Criacao de loja - Proposta VT Comercio Digital - ACCOUNT NAME [" + cCodCli + "] - [" + cNomeCli + "]"

    // Simula outras Configurações baseadas no ambiente
    cLogLevel := ""
    cUrlApi := ""
    nTimeout := 0

    If oEnvControl:IsProduction()
        cLogLevel := "ERROR"
        cUrlApi := "https://api.producao.vtex.com.br"
        nTimeout := 30
    ElseIf oEnvControl:IsStaging()
        cLogLevel := "WARN"
        cUrlApi := "https://api.homologacao.vtex.com.br"
        nTimeout := 60
    Else
        cLogLevel := "DEBUG"
        cUrlApi := "https://api.desenvolvimento.vtex.com.br"
        nTimeout := 120
    EndIf

    // Monta resultado do teste
    cMsg := "Simulação GCVWFVTEX REFATORADA:" + CRLF + CRLF
    cMsg += "Parâmetros DE ENTRADA:" + CRLF
    cMsg += "Proposta: " + cProposta + CRLF
    cMsg += "Cliente: " + cCodCli + CRLF
    cMsg += "Data WF: " + DtoC(dDataWF) + CRLF + CRLF

    cMsg += "AMBIENTE DETECTADO:" + CRLF
    cMsg += "Nome: " + oEnvControl:GetCurrentEnv() + CRLF
    cMsg += "Tipo: " + oEnvControl:GetEnvironmentType() + CRLF
    cMsg += "É Produção: " + IIF(oEnvControl:IsProduction(), "Sim", "Não") + CRLF + CRLF

    cMsg += "Configurações APLICADAS:" + CRLF
    cMsg += "Email destino: " + cEmail + CRLF
    cMsg += "Assunto: " + cAssunto + CRLF
    cMsg += "Log Level: " + cLogLevel + CRLF
    cMsg += "URL API: " + cUrlApi + CRLF
    cMsg += "Timeout: " + AllTrim(Str(nTimeout)) + "s" + CRLF + CRLF

    cMsg += "ARQUIVOS:" + CRLF
    cMsg += "Path HTML: " + cPathHTML + CRLF
    cMsg += "Arquivo HTML: " + cArqHTML + CRLF + CRLF

    cMsg += "STATUS: Simulação executada com sucesso! ?"

    // Log no console
    ConOut("=== RESULTADO DA Simulação ===")
    ConOut("Ambiente: " + oEnvControl:GetCurrentEnv() + " (" + oEnvControl:GetEnvironmentType() + ")")
    ConOut("Email: " + cEmail)
    ConOut("URL API: " + cUrlApi)
    ConOut("Log Level: " + cLogLevel)
    ConOut("Timeout: " + AllTrim(Str(nTimeout)) + "s")

    MsgInfo(cMsg, "Simulação GCVWFVTEX")

Catch oError
    lRet := .F.
    cMsg := "ERRO na Simulação GCVWFVTEX: " + oError:Description
    ConOut(cMsg)
    MsgAlert(cMsg, "Erro")

Finally
    // Libera o objeto
    If oEnvControl != Nil
        FreeObj(oEnvControl)
        oEnvControl := Nil
    EndIf

EndTry

ConOut("TESTE 8 FINALIZADO - Sucesso: " + IIF(lRet, "Sim", "Não"))
ConOut(Replicate("-", 60))

Return lRet

//------------------------------------------------------------------
/*/{Protheus.doc} U_TSTENV09
Teste da função U_TIEnv() - Informações do ambiente em JSON

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENV09()

Local jEnvInfo := Nil
Local cMsg := ""
Local cJsonString := ""

ConOut(Replicate("=", 60))
ConOut("TESTE 9: FUNÇÃO U_TIENV()")
ConOut(Replicate("=", 60))

// Executa a função TIEnv
jEnvInfo := U_TIEnv()

If jEnvInfo != Nil .And. jEnvInfo['success']
    // Monta mensagem com informações principais
    cMsg := "FUNÇÃO U_TIENV() - RESULTADO:" + CRLF + CRLF
    cMsg += "? Execução: SUCESSO" + CRLF
    cMsg += "Timestamp: " + jEnvInfo['timestamp'] + CRLF + CRLF

    cMsg += "AMBIENTE:" + CRLF
    cMsg += "Nome: " + jEnvInfo['environment']['name'] + CRLF
    cMsg += "Tipo: " + jEnvInfo['environment']['type'] + CRLF
    cMsg += "Produção: " + IIF(jEnvInfo['environment']['isProduction'], "Sim", "NÃO") + CRLF
    cMsg += "Pré-Produção: " + IIF(jEnvInfo['environment']['isPreProduction'], "Sim", "NÃO") + CRLF
    cMsg += "Staging: " + IIF(jEnvInfo['environment']['isStaging'], "Sim", "NÃO") + CRLF
    cMsg += "Desenvolvimento: " + IIF(jEnvInfo['environment']['isDevelopment'], "Sim", "NÃO") + CRLF + CRLF
    cMsg += "Descrição: " + jEnvInfo['environment']['description'] + CRLF
    cMsg += "É MI: " + IIF(jEnvInfo['environment']['isMI'], "Sim", "NÃO") + CRLF + CRLF

    cMsg += "SESSÃO ATUAL:" + CRLF
    cMsg += "Empresa: " + jEnvInfo['session']['company'] + CRLF
    cMsg += "Filial: " + jEnvInfo['session']['filial'] + CRLF
    cMsg += "Usuário: " + jEnvInfo['session']['user'] + " - " + jEnvInfo['session']['userName'] + CRLF
    cMsg += "Estação: " + jEnvInfo['session']['station'] + CRLF + CRLF
    cMsg += "Thread: " + AllTrim(Str(jEnvInfo['session']['thread'])) + CRLF + CRLF

    cMsg += "SESSÃO TECNICA:" + CRLF
    cMsg += "Servidor: " + jEnvInfo['technical']['server'] + CRLF
    cMsg += "Banco de Dados: " + jEnvInfo['technical']['database'] + CRLF
    cMsg += "RPO: " + jEnvInfo['technical']['rpoDate'] + CRLF
    cMsg += "Build: " + jEnvInfo['technical']['buildVersion'] + CRLF + CRLF

    cMsg += "SESSÃO PARAMETROS:" + CRLF
    cMsg += "Parametro TI_AMBPRO - Ambiente PRO: " + jEnvInfo['parameters']['TI_AMBPRO'] + CRLF
    cMsg += "Parametro TI_AMBPRE - Ambiente PRE: " + jEnvInfo['parameters']['TI_AMBPRE'] + CRLF
    cMsg += "Parametro TI_AMBHOM - Ambiente STG: " + jEnvInfo['parameters']['TI_AMBHOM'] + CRLF
    cMsg += "Parametro TI_AMBDEV - Ambiente DEV: " + jEnvInfo['parameters']['TI_AMBDEV'] + CRLF + CRLF

    // Log no console
    ConOut("=== RESULTADO TIENV ===")

    // Converte para JSON string
    cJsonString := jEnvInfo:ToJson()
    ConOut("=== JSON COMPLETO ===")
    ConOut(cJsonString)

    MsgInfo(cMsg, "Teste 9 - Função TIEnv()")

Else
    cMsg := "ERRO na execução da função U_TIEnv():" + CRLF + CRLF
    If jEnvInfo != Nil .And. jEnvInfo:HasProperty('error')
        cMsg += "Descrição: " + jEnvInfo['error']['message'] + CRLF
        cMsg += "Operação: " + jEnvInfo['error']['operation']
    Else
        cMsg += "Função retornou Nil ou estrutura inválida"
    EndIf

    ConOut("ERRO: " + cMsg)
    MsgAlert(cMsg, "Erro no Teste 9")
EndIf

ConOut("TESTE 9 FINALIZADO")
ConOut(Replicate("-", 60))

Return

//------------------------------------------------------------------
/*/{Protheus.doc} U_TSTENV10
Teste de integração entre TIEnv() e EnvironmentControl

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
/*/
//------------------------------------------------------------------

User Function TSTENV10()

Local jEnvInfo := Nil
Local oEnvControl := Nil
Local cMsg := ""
Local lIntegracao := .T.

ConOut(Replicate("=", 60))
ConOut("TESTE 10: INTEGRAÇÃO TIENV x ENVIRONMENTCONTROL")
ConOut(Replicate("=", 60))

// Executa ambas as funções
jEnvInfo := U_TIEnv()
oEnvControl := Shared.Utils.EnvironmentControlUtils.EnvironmentControl():New()

If jEnvInfo != Nil .And. jEnvInfo['success'] .And. oEnvControl != Nil
    cMsg := "TESTE DE INTEGRAÇÃO:" + CRLF + CRLF

    // Compara nome do ambiente
    If jEnvInfo['environment']['name'] != oEnvControl:GetCurrentEnv()
        lIntegracao := .F.
        cMsg += "? Nome do ambiente: INCONSISTENTE" + CRLF
        cMsg += "  TIEnv: " + jEnvInfo['environment']['name'] + CRLF
        cMsg += "  EnvironmentControl: " + oEnvControl:GetCurrentEnv() + CRLF
    Else
        cMsg += "? Nome do ambiente: CONSISTENTE" + CRLF
    EndIf

    // Compara tipo do ambiente
    If jEnvInfo['environment']['type'] != oEnvControl:GetEnvironmentType()
        lIntegracao := .F.
        cMsg += "? Tipo do ambiente: INCONSISTENTE" + CRLF
        cMsg += "  TIEnv: " + jEnvInfo['environment']['type'] + CRLF
        cMsg += "  EnvironmentControl: " + oEnvControl:GetEnvironmentType() + CRLF
    Else
        cMsg += "? Tipo do ambiente: CONSISTENTE" + CRLF
    EndIf

    // Compara flags
    If jEnvInfo['environment']['isProduction'] != oEnvControl:IsProduction()
        lIntegracao := .F.
        cMsg += "? Flag isProduction: INCONSISTENTE" + CRLF
    Else
        cMsg += "? Flag isProduction: CONSISTENTE" + CRLF
    EndIf

    If jEnvInfo['environment']['isPreProduction'] != oEnvControl:IsPreProduction()
        lIntegracao := .F.
        cMsg += "? Flag isPreProduction: INCONSISTENTE" + CRLF
    Else
        cMsg += "? Flag isPreProduction: CONSISTENTE" + CRLF
    EndIf

    If jEnvInfo['environment']['isStaging'] != oEnvControl:IsStaging()
        lIntegracao := .F.
        cMsg += "? Flag isStaging: INCONSISTENTE" + CRLF
    Else
        cMsg += "? Flag isStaging: CONSISTENTE" + CRLF
    EndIf

    If jEnvInfo['environment']['isDevelopment'] != oEnvControl:IsDevelopment()
        lIntegracao := .F.
        cMsg += "? Flag isDevelopment: INCONSISTENTE" + CRLF
    Else
        cMsg += "? Flag isDevelopment: CONSISTENTE" + CRLF
    EndIf

    cMsg += CRLF + "RESULTADO GERAL:" + CRLF
    If lIntegracao
        cMsg += "?? INTEGRAÇÃO PERFEITA! ??" + CRLF
        cMsg += "TIEnv() e EnvironmentControl estão sincronizados."
    Else
        cMsg += "?? PROBLEMAS DE INTEGRAÇÃO ??" + CRLF
        cMsg += "Há inconsistências entre TIEnv() e EnvironmentControl."
    EndIf

    // Log no console
    ConOut("=== RESULTADO INTEGRAÇÃO ===")
    ConOut("Integração: " + IIF(lIntegracao, "OK", "ERRO"))
    ConOut("TIEnv - Ambiente: " + jEnvInfo['environment']['name'])
    ConOut("TIEnv - Tipo: " + jEnvInfo['environment']['type'])
    ConOut("EnvironmentControl - Ambiente: " + oEnvControl:GetCurrentEnv())
    ConOut("EnvironmentControl - Tipo: " + oEnvControl:GetEnvironmentType())

    MsgInfo(cMsg, "Teste 10 - Integração")

Else
    cMsg := "ERRO: Não foi possível executar o teste de integração" + CRLF + CRLF
    If jEnvInfo == Nil
        cMsg += "- TIEnv() retornou Nil" + CRLF
    ElseIf !jEnvInfo['success']
        cMsg += "- TIEnv() indicou falha" + CRLF
    EndIf
    If oEnvControl == Nil
        cMsg += "- EnvironmentControl não foi criado" + CRLF
    EndIf

    ConOut("ERRO: " + cMsg)
    MsgAlert(cMsg, "Erro no Teste 10")
EndIf

// Libera objeto
If oEnvControl != Nil
    FreeObj(oEnvControl)
    oEnvControl := Nil
EndIf

ConOut("TESTE 10 FINALIZADO")
ConOut(Replicate("-", 60))

Return


/* /{Protheus.doc} U_TSTENV11
Teste de seleção de email com TIEmailEnv()

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
*/
User Function TSTENV11()
Local cEmail := U_TIEmailEnv("<EMAIL>", "<EMAIL>")

Try
    MsgInfo("Email: " + cEmail, "Teste 11 - Selecão de Email")
Catch oError
    MsgAlert("Erro: " + oError:Description, "Erro no Teste 11")
EndTry
Return

/*/{Protheus.doc} U_TSTENV12
Teste de Verificação se é ambiente MI com TIVerifyEnv()

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
*/
User Function TSTENV12()
Local cMsg := ""

Try
    cMsg += " Ambiente é: " + Shared.Utils.U_TIEnvType() + " - Teste 12 - Verificação sem local " + CRLF
    cMsg += " Ambiente é: " + Shared.Utils.U_TIEnvType(.t.) + " - Teste 12 - Verificação com local "

    MsgInfo(cMsg, "Teste 12 - Verificação qual é o ambiente")
Catch oError    
    MsgAlert("Erro: " + oError:Description, "Erro no Teste 12")
EndTry

Return

/* /{Protheus.doc} U_TSTENV13
Teste de Verificação do tipo de ambiente com TIVerifyEnv()

@type function
@version 1.0
<AUTHOR>
@since 27/08/2025
*/
User Function TSTENV13()
Local cIsType := ""

Try
    cIsType += "É Produção BR: " +  IIf(Shared.Utils.U_TIVerifyEnv("PROD","BR"), "Sim", "NÃO") + CRLF
    cIsType += "É Pré-Produção BR: " +  IIf(Shared.Utils.U_TIVerifyEnv("PRE","BR"), "Sim", "NÃO") + CRLF
    cIsType += "É Homologação BR: " +  IIf(Shared.Utils.U_TIVerifyEnv("STG","BR"), "Sim", "NÃO") + CRLF
    cIsType += "É Desenvolvimento BR: " +  IIf(Shared.Utils.U_TIVerifyEnv("DEV","BR"), "Sim", "NÃO") + CRLF
    cIsType += "É Produção MI: " +  IIf(Shared.Utils.U_TIVerifyEnv("PROD","MI"), "Sim", "NÃO") + CRLF
    cIsType += "É Pré-Produção MI: " +  IIf(Shared.Utils.U_TIVerifyEnv("PRE","MI"), "Sim", "NÃO") + CRLF
    cIsType += "É Homologação MI: " +  IIf(Shared.Utils.U_TIVerifyEnv("STG","MI"), "Sim", "NÃO") + CRLF
    cIsType += "É Desenvolvimento MI: " +  IIf(Shared.Utils.U_TIVerifyEnv("DEV","MI"), "Sim", "NÃO")

    MsgInfo(cIsType, "Teste 13 - Verificação tipo de ambiente")

Catch oError
    MsgAlert("Erro: " + oError:Description, "Erro no Teste 13")
EndTry

Return
