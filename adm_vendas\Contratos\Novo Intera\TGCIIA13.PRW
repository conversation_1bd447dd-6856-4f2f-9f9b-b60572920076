#INCLUDE "PROTHEUS.CH"

Static __cEnvSrv := ""
Static __cIPSrv  := ""
Static __cPorSrv := "0"
Static __cMyIp   := NIL
Static __cMyPort := NIL

#xtranslate AMtoCmp( <v1> )       => Right(<v1>, 2) + "/" + Left(<v1>, 4)
#xtranslate CmptoAM( <v1> )       => Right(<v1>, 4) + Left(<v1>, 2)
#xtranslate SomaAM(<cAM>, <nQ>) => eval({|x, y| cAM:= x, aA := Array(y), aeval(aA, {|| cAM := If(Right(cAM, 2) == "12", soma1(Left(cAM, 4)) + "01", soma1(cAM))}), cAM}, <cAM>, <nQ>)
#xtranslate TiraAM(<cAM>, <nQ>) => eval({|x, y| cAM:= x, aA := Array(y), aeval(aA, {|| cAM := If(Right(cAM, 2) == "01", Tira1(Left(cAM, 4)) + "12", Tira1(cAM))}), cAM}, <cAM>, <nQ>)


/*
TIADMVIN-3138
U_GCIIA13V()                       //Rotina para processar "Inicio do mes de contratos" em Tela informando o mes
U_GCIIA13X()                       // Parametros e Job para executar  o gerenciados das Multithread
U_GCIIA13M()                       // Monitor das Threads
U_GCIIA13J()                       // Job para executar  o gerenciados das Multithread para Inicio do Mes
U_GCIIA13G()                       // Distribuição contratos a serem processados em Multithread
U_GCIIA13T(cChaveTHR, cDir, cPar)  // Rotina utlizada na Thread para processar "Inicio do mes de contratos" por contato
U_GCIIA13P(cChave)                 // Faz a pergunta e sava no diretorio do processamento das threads
U_GCIIA13L(cStatus, cObs, cIdProc, cUserId, cTPOper)   // Cria/atualiza o log PHS
U_GCIIA13H(cAMRef)                                     // retorna o id 
*/

User Function GCIIA13V()
	Local aRetPar    := {}
    Local cAniver    := ""
    
    Private lAbortPrint := .F.
    aRetPar := PegaParCmp()
    If Empty(aRetPar)
        Return
    EndIf
    
    cAniver    := Alltrim(RetPar(aRetPar,  "cAMAnive"   ))

    If ! MsgYesNo("Confirma o Fechamento Fotografia [" + AMtoCmp(cAniver) + "]?")
        Return
    EndIf

    Processa({|| FechaFotoCmp(aRetPar, cAniver)}, "Fechamento Fotografia","Buscando dados...", .T.) 

    If lAbortPrint
        MsgAlert("Geração Interrompida!")
    Else
        MsgAlert("Geração Finalizada!")
    EndIf
    
Return 


Static Function PegaParCmp(lJob)
	Local cLstCli  := ""
	Local aRet     := {}
    Local dDtRef   := dDataBase

    Default lJob    := .F.

    cNomeMA  := MesExtenso(dDtRef) + " de " + Str(Year(dDtRef), 4)
	cTexto   := "Deseja processar comprovação/reajuste de índice/multa dos contratos vencidos de " + cNomeMA
	cTitulo  := "Processamento Intera Anual - "+ cNomeMA
	cAMAnive := Left(Dtos(dDtRef), 6)

    If Day(dDtRef) >= 22
		cNomeMA := MesExtenso(LastDay(dDtRef) + 1) + " de " + Str(Year(LastDay(dDtRef) + 1), 4)
		cTexto  := "Deseja efetuar o processamento antecipado do reajuste anual Intera com Contratos que farão aniversário no próximo mês, de " + cNomeMA
		cTitulo := "Processamento Intera anual antecipado - "+ cNomeMA
		cAMAnive := Left(Dtos(LastDay(dDtRef) + 1), 6)
	EndIf 

    If ! lJob 
        If ! MsgYesNo(cTexto, cTitulo)
		    Return 
	    EndIf
        If ! Aviso( "Filtro Clientes", @cLstCli ,{"Ok","Cancelar"},3,"Informe a lista de clientes separados por virgula" ,,,.T.) == 1
            Return {}
        EndIf
        If ! Empty(cLstCli) .and. Right(Alltrim(cLstCli), 1) <> ","
            cLstCli += ","
        EndIf
    EndIf 
   
    aadd(aRet, {Upper("cLstCli")  , cLstCli}) 
    aadd(aRet, {Upper("cUserId")  , __cUserId}) 
    aadd(aRet, {Upper("cAMAnive") , cAMAnive}) 


Return aClone(aRet)


Static Function RetPar(aParBox, cNomPar)
	Local np:= 0
	
	np := Ascan(aParBox, {|x| Upper(Alltrim(x[1])) == Upper(AllTrim(cNomPar))} )
	If Empty(np)
		Return ""
	EndIf
    If !Empty(aParBox[np, 2]) .and. ValType(aParBox[np, 2]) == "C"
        Return AllTrim(aParBox[np, 2])
    EndIf
Return aParBox[np, 2]



Static Function FechaFotoCmp(aRetPar, cAniver)
    Local aArea       := GetArea()
    Local aAreaPHS    := PHS->(GetArea())	
    Local cTMP        := ""
    Local cAMProc     := Left(Dtos(dDataBase), 6)
    Local cQuery      := ""
    Local nRecnoPQB   := 0  
    Local nRecnoPQS   := 0  
    Local cTime       := StrTran(Time(), ":", "")
	Local cIdProc     := ""
	Local cChvIDFF    := ""    
    Local cCliente    := ""
    Local cUserId     := "" 
    Local cLstCli     := ""
    Local cTPOpeFF    := "000024"
    Local cNumCXK     := ""

    
    cUserId    := Alltrim(RetPar(aRetPar,  "cUserId"  ))
    cLstCli    := Alltrim(RetPar(aRetPar,  "cLstCli"  ))
    cAniver    := Alltrim(RetPar(aRetPar,  "cAMAnive" ))

    ProcRegua(1)
   
    cIDProc := BuscaPHT(cAniver)
    If Empty(cIDProc)
        cIdProc     := Padr(DtoS(MsDate()) + cTime + "_" + StrZero(Randomize(10, 1000), 3), 18)
        cTMP := MontaQry1(aRetPar, @cQuery, cAniver)
        If Empty(cTMP)
            Alert("Não foi encontrado dados com esses parametros!")
            Return 
        EndIf
    
        CriaPHT(cIdProc, cAMProc, aRetPar, cTPOpeFF, cAniver)
        While  (cTMP)->(! Eof())
            nRecnoPQB := (cTMP)->RECNO
            cCliente  := (cTMP)->PQB_CODCLI
            If ! IsBlind()
                IncProc("Criando Log Fechamento Fotografia cliente: " + cCliente )
                ProcessMessage()
            EndIf

            PQB->(DbGoto(nRecnoPQB))
            LogFF(" ", "", cIdProc, cUserId, cTPOpeFF)
    
            (cTMP)->(DbSkip())
        End
        (cTMP)->(DbCloseArea())

    EndIf 

    //cNumCXK	:= GetSXeNum("CXK","CXK_NUMERO")
    //ConfirmSX8()

    PHS->(DbSetOrder(6)) // PHS_FILIAL+PHS_IDPROC+PHS_CONTRA+PHS_REVISA+PHS_TPOPER+PHS_GRUPO+PHS_UNINEG
    cChvIDFF := FwxFilial("PHS") + cIdProc  
    PHS->(DbSeek(cChvIDFF))
    While  PHS->(! Eof()) .And. cChvIDFF == PHS->(PHS_FILIAL + PHS_IDPROC )
        nRecnoPQS := PHS->(Recno())
        cCliente  := PHS->PHS_CLIENT

        If ! IsBlind()
            IncProc("Cliente: " + cCliente   )
            ProcessMessage()
        EndIf
        
        If ! Empty(cLstCli)
            If ! cCliente $ cLstCli
                PHS->(DbSkip())
                Loop 
            EndIf 
        Else 
            If PHS->PHS_STATUS == "2" 
                PHS->(DbSkip())
                Loop 
            EndIf 
        EndIF 

        PHS->(RecLock("PHS"))
        PHS->PHS_STATUS := " "
        PHS->(MsUnlock())


        U_GCIIA13A(nRecnoPQS, cIdProc, cAniver, cNumCXK)
        PHS->(DbSkip())
        
    End

	FimPHT(cIdProc, cTPOpeFF)

    RestArea(aAreaPHS) 
    RestArea(aArea) 

    U_GCIIA13F(NIL, cAniver)    // envio de email
Return


 
Static Function MontaQry1(aRetPar, cQuery, cAniver)
    Local cTMP      := GetNextAlias()
    Local cAMAnt    := TiraAM(cAniver, 12)
        
	cQuery	:= " SELECT PQB.R_E_C_N_O_ AS RECNO, PQB.PQB_CODCLI "
	cQuery	+= "   FROM " + RetSqlName("PQB") + " PQB "
	cQuery	+= "  WHERE PQB.PQB_FILIAL = '" + xFilial("PQB") + "'"
	cQuery	+= "    AND PQB.PQB_STATUS = 'A' " 
	cQuery	+= "    AND (PQB.PQB_ANIVER = '" + cAMAnt + "' OR  PQB.PQB_ANIVER = '" + cAniver + "') "
	cQuery	+= "    AND PQB.D_E_L_E_T_ = ' ' "

    dbUseArea(.T., "TOPCONN", TCGenQry(,, cQuery), cTMP, .t., .t.)

	If (cTMP)->(Eof())		
        (cTMP)->(DbCloseArea())
        Return ""
    EndIf

Return cTMP

User Function GCIIA13H(cAMRef, cTipOPe)

Return BuscaPHT(cAMRef, cTipOPe)

Static Function BuscaPHT(cAMRef, cTipOPe)
    Local cTMP      := GetNextAlias()
    Local cIDProc   := "" 
    Local cQuery    := ""
    Local cMARef    := ""   

    Default cAMRef  := Left(Dtos(dDataBase), 6)   
    Default cTipOPe := '000024'

    cMARef := AMtoCmp(cAMRef)

    cQuery := " "
    cQuery += " SELECT PHT_IDPROC     " 
    cQuery += " FROM   " + RetSQLName("PHT") + "            " 
    cQuery += " WHERE  D_E_L_E_T_ = ' '            " 
    cQuery += "        AND PHT_TPOPER = '" + cTipOPe + "'    " 
    cQuery += "        AND PHT_COMPET = '" + cMARef  + "' " 
	
    dbUseArea(.T., "TOPCONN", TCGenQry(,, cQuery), cTMP, .t., .t.)

    If ! (cTMP)->(Eof())		
        cIDProc := (cTMP)->PHT_IDPROC
    EndIf

    (cTMP)->(DbCloseArea())

Return cIDProc




/*
 ###############################################  Rotinas a serem  utilizados em multi-thread  ###########################################
*/
User Function GCIIA13M()
    Local cTitulo := "Fechamento Fotografia"
    Local nqThread := 10
    Local cChave := "FECHAMENTOFOTO"
    Local cRotJob:= "U_GCIIA13G"  // rotina com a query
    Local cRotThr:= "U_GCIIA13T"  // rotina da ponta na thread
    Local cRotPar:= "U_GCIIA13P"
    Local cRotErr:= "U_GCIIA13E"  // funcao para tratamento de erro na thread U_GCIIA13T
    Local cRotEnd:= "U_GCIIA13F"
 
    U_TGCVJMON(cTitulo, cChave, nqThread, cRotJob, cRotThr, cRotPar, cRotErr , cRotEnd)

Return 


User Function GCIIA13X()
    Local nqThread  := 10
    Local cChave    := "FECHAMENTOFOTO"
    Local cChaveSrv := ""
    Local cRotJob   := "U_GCIIA13G"  // rotina com a query
    Local cRotThr   := "U_GCIIA13T"  // rotina da ponta na thread
    Local cRotPar   := "U_GCIIA13P"
    Local cRotErr   := "U_GCIIA13E"  // funcao para tratamento de erro na thread U_GCIIA13T
    Local cRotEnd   := "U_GCIIA13F"

    cChaveSrv := "SRV_" + cChave

    Private lAbortPrint := .F.

    If ! U_JOnLine(cChave)
        U_JMsg(cChaveSrv, "Iniciando....")       
        
        If ! U_JGetPar(cChave, nqThread, cRotJob, cRotThr, cRotPar, cRotErr, cRotEnd)
            Return
        EndIf 
    Else 
        MsgAlert("Job em execução...")
    EndIf 
        
    TFFRun()
    
Return 

Static Function TFFRun()
    Local cTitulo := "Fechamento Fotografia"
    Local cChave  := "FECHAMENTOFOTO"
    Local oPanel1
    Local oDlg                 
    Local lOk     := .F.    
    Local oTimer  
    Local oRodaPe 
    Local oFontB  := TFont():New('Consolas',, 16,, .T.,,,,, .F., .F.) 
    
    Private lOnline := .F.

    lOnline := U_JOnLine(cChave)
    
    DEFINE MSDIALOG oDlg TITLE "Processando Threads - " + cTitulo  FROM 0, 0 TO 380, 560 PIXEL OF oMainWnd

        oPanel1 :=TPanel():New( 010, 010, ,oDlg, , , , , , 14, 14, .F.,.T. )
        oPanel1 :align := CONTROL_ALIGN_TOP

        oBPS1  := THButton():New(002, 002, "Ocultar"   , oPanel1, {|| oDlg:End()       }, 50, 10, oFontB, "Fechar essa janela e permitir a execução do job") 
        oBPS2  := THButton():New(002, 052, "Finalizar" , oPanel1, {|| Finaliza(cChave) }, 40, 10, oFontB, "Parar o serviço de execução do job") 
        oBPS3  := THButton():New(002, 092, "Monitor"   , oPanel1, {|| U_GCIIA13M()     }, 40, 10, oFontB, "Monitor threads") 
        oBPS4  := THButton():New(002, 132, "Log"       , oPanel1, {|| TelaLog()        }, 40, 10, oFontB, "Monitor Logs") 

         
        oRodaPe:= TSimpleEditor():New( 0,0,oDlg, 40, 40 )
        oRodaPe:Align := CONTROL_ALIGN_ALLCLIENT
        
        DEFINE TIMER oTimer INTERVAL 1000 ACTION AtuTela2(oTimer, oDlg, cTitulo, cChave, oRodaPe ) OF oDlg

    ACTIVATE MSDIALOG oDlg ON INIT (AtuTela2(oTimer, oDlg,  cTitulo, cChave, oRodaPe), oTimer:Activate())  CENTERED
    
 
Return  lOk    

Static Function TelaLog()
    Local cIdProc := ""
    Local aRetPar    := {}
    Local cAniver    := ""
    Local cChave  := "FECHAMENTOFOTO"


    aRetPar := U_JLoadPar(cChave)
    cAniver := Alltrim(RetPar(aRetPar,  "cAMAnive"   ))
    
    cIdProc :=  BuscaPHT(cAniver)

    If ! Empty(cIdProc)
        U_TGCVC004(cIdProc, ""      , "Monitor Thread - Fechamento Fotografia")    
    Else 
        U_TGCVC004(""     , "000024", "Monitor Thread - Fechamento Fotografia")
    EndIf 


Return 

Static Function AtuTela2(oTimer, oDlg, cTitulo, cChave, oRodaPe)
    Local cChaveSrv := "SRV_" + cChave 
    
    If oTimer == NIL
        Return
    EndIf 
    
    oTimer:Deactivate()   
    lOnline:= U_JOnLine(cChave)
    
    cMsgSrv := U_JMsg(cChaveSrv)

    oRodaPe:Load(MontaHtml(cChaveSrv, cMsgSrv, lOnline))
    oRodaPe:Refresh()

    oDlg:cCaption := "Processamento Threads  - " + cTitulo + " - " + Time() +If(lOnline," - (On Line) ", " - (Off Line)")
  
    oTimer:Activate()

Return

Static Function Finaliza(cChave)
    If MsgYesNo("Confirma a desativação do serviço")
        U_JSaveArq("SRV_" + cChave + ".fim", "Fim") 
    Endif

Return 

Static Function MontaHtml(cChaveSrv, cMsgSrv, lOnline)
    Local aInfo := U_JSrvGetInfo(cChaveSrv)
    Local nTCapa   := aInfo[1]
    Local nLimite  := aInfo[2]
    Local nCount   := aInfo[3]
    Local nQtdGo   := aInfo[4]
    Local nQtdProc := aInfo[5]
    Local nQtdErro := aInfo[6]
    Local aQtdPHS  := BuscQtd()
    Local nQRegQr  := aQtdPHS[1]
    Local nQInici  := aQtdPHS[2]
    Local nQConcl  := aQtdPHS[3]
    Local nQInvav  := aQtdPHS[4]
    Local nQErro   := aQtdPHS[5]
    Local nQNProc  := aQtdPHS[6]
    Local nQBloque := aQtdPHS[7]
    Local nPExec   := 0

    nPExec := Int((nQConcl  + nQInvav + nQErro + nQBloque ) / nQRegQr * 100)
    
    cHtml := ""
    cHtml += "   <table width=100% border=0 cellspacing=0 cellpadding=2 bordercolor='666633'>" + CRLF
    If lOnline
        cHtml += "      <tr><td width='150' align='LEFT'><b><font COLOR='GREEN'>On Line</font></b></td> 
    Else 
        cHtml += "      <tr><td width='150' align='LEFT'><b><font COLOR='RED'>OFF Line</font></b></td> 
    EndIf 
    cHtml += "          <td width='300' align='LEFT'><font COLOR='BLUE'><b>" + cMsgSrv  +"</b></font></td></tr>" + CRLF
    cHtml += "   </table>" + CRLF

    cHtml += "<br>" + CRLF
    cHtml += "<br>" + CRLF

    cHtml += "<b>Contratos - quantidades</b>" + CRLF
    cHtml += "   <table width=100% border=1 cellspacing=0 cellpadding=2 bordercolor='666633'>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Contratos</b></td>    <td width='150' align='LEFT'> " + Alltrim(Transform(nQRegQr , "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Iniciados</b></td>    <td width='150' align='LEFT'> " + Alltrim(Transform(nQInici , "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Concluidos</b></td>   <td width='150' align='LEFT'> " + Alltrim(Transform(nQConcl , "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Ocorrencias</b></td>  <td width='150' align='LEFT'> " + Alltrim(Transform(nQInvav , "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Erros</b></td>        <td width='150' align='LEFT'> " + Alltrim(Transform(nQErro  , "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Bloqueados</b></td>   <td width='150' align='LEFT'> " + Alltrim(Transform(nQBloque, "@e 99,999,999,999"))  +"</td></tr>" + CRLF    
    cHtml += "      <tr><td width='150' align='LEFT'><b>Não iniciados</b></td><td width='150' align='LEFT'> " + Alltrim(Transform(nQNProc , "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>% Executado</b></td>  <td width='150' align='LEFT'> " + Alltrim(Transform(nPExec , "@e 999"))              +"</td></tr>" + CRLF
    cHtml += "   </table>" + CRLF
    cHtml += "<b>Job em Execução - Threads</b>" + CRLF
    cHtml += "   <table width=100% border=1 cellspacing=0 cellpadding=2 bordercolor='666633'>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Capacidade</b></td>      <td width='150' align='LEFT'> " + Alltrim(Transform(nTCapa  , "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Disponibilidade</b></td> <td width='150' align='LEFT'> " + Alltrim(Transform(nLimite , "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Iniciadas</b></td>       <td width='150' align='LEFT'> " + Alltrim(Transform(nCount  , "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Distribuidos</b></td>    <td width='150' align='LEFT'> " + Alltrim(Transform(nQtdGo  , "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Processados</b></td>     <td width='150' align='LEFT'> " + Alltrim(Transform(nQtdProc, "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "      <tr><td width='150' align='LEFT'><b>Erros</b></td>           <td width='150' align='LEFT'> " + Alltrim(Transform(nQtdErro, "@e 99,999,999,999"))  +"</td></tr>" + CRLF
    cHtml += "   </table>" + CRLF

Return cHtml 

Static Function BuscQtd()
    Local clAlias := GetNextAlias()
	Local cQuery := ""	
    Local aArea  := GetArea()
    Local aQtdes := {}  

    Local cIdProc := ""
    Local aRetPar    := {}
    Local cAniver    := ""
    Local cChave  := "FECHAMENTOFOTO"


    aRetPar := U_JLoadPar(cChave)
    cAniver := Alltrim(RetPar(aRetPar,  "cAMAnive"   ))


    cIdProc := BuscaPHT(cAniver)

    If Empty(cIdProc)
        Return {0, 0, 0, 0, 0, 0, 0}
    EndIf

    cQuery := QryMon(cIDProc) 
    
    dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), clAlias, .T., .F. )
    If (clAlias)->(Eof())
        (clAlias)->(dbCloseArea())
        RestArea(aArea)
        Return {0, 0, 0, 0, 0, 0, 0}
    EndIf 

    aadd(aQtdes, (clAlias)->QTDREG )    
    aadd(aQtdes, (clAlias)->QTDINI )    
    aadd(aQtdes, (clAlias)->QTDCONC )    
    aadd(aQtdes, (clAlias)->QTDINVAL )    
    aadd(aQtdes, (clAlias)->QTDERRO  )    
    aadd(aQtdes, (clAlias)->QTDNPROC )
    aadd(aQtdes, (clAlias)->QTDBLOQU )
    
    (clAlias)->(dbCloseArea())
    RestArea(aArea)
Return aQtdes


Static Function QryMon(cIDProc)
		Local cQuery := ""

		cQuery := " SELECT (SELECT COUNT(1) FROM "+ RetSqlName("PHS") +" A WHERE A.PHS_FILIAL = '"+FWxFilial("PHS")+"' AND	A.PHS_IDPROC = '"+ cIDProc +"' AND                        A.PHS_TPOPER = PHS.PHS_TPOPER AND A.D_E_L_E_T_ = ' ' ) AS QTDREG,   "
		cQuery += "        (SELECT COUNT(1) FROM "+ RetSqlName("PHS") +" A WHERE A.PHS_FILIAL = '"+FWxFilial("PHS")+"' AND	A.PHS_IDPROC = '"+ cIDProc +"' AND A.PHS_STATUS = '1' AND A.PHS_TPOPER = PHS.PHS_TPOPER AND A.D_E_L_E_T_ = ' ' ) AS QTDINI,   "
        cQuery += "        (SELECT COUNT(1) FROM "+ RetSqlName("PHS") +" A WHERE A.PHS_FILIAL = '"+FWxFilial("PHS")+"' AND	A.PHS_IDPROC = '"+ cIDProc +"' AND A.PHS_STATUS = '2' AND A.PHS_TPOPER = PHS.PHS_TPOPER AND A.D_E_L_E_T_ = ' ' ) AS QTDCONC,  "
        cQuery += "        (SELECT COUNT(1) FROM "+ RetSqlName("PHS") +" A WHERE A.PHS_FILIAL = '"+FWxFilial("PHS")+"' AND	A.PHS_IDPROC = '"+ cIDProc +"' AND A.PHS_STATUS = '3' AND A.PHS_TPOPER = PHS.PHS_TPOPER AND A.D_E_L_E_T_ = ' ' ) AS QTDERRO,  "
		cQuery += "        (SELECT COUNT(1) FROM "+ RetSqlName("PHS") +" A WHERE A.PHS_FILIAL = '"+FWxFilial("PHS")+"' AND	A.PHS_IDPROC = '"+ cIDProc +"' AND A.PHS_STATUS = '4' AND A.PHS_TPOPER = PHS.PHS_TPOPER AND A.D_E_L_E_T_ = ' ' ) AS QTDINVAL, "
        cQuery += "        (SELECT COUNT(1) FROM "+ RetSqlName("PHS") +" A WHERE A.PHS_FILIAL = '"+FWxFilial("PHS")+"' AND	A.PHS_IDPROC = '"+ cIDProc +"' AND A.PHS_STATUS = '5' AND A.PHS_TPOPER = PHS.PHS_TPOPER AND A.D_E_L_E_T_ = ' ' ) AS QTDBLOQU, "        
		cQuery += "        (SELECT COUNT(1) FROM "+ RetSqlName("PHS") +" A WHERE A.PHS_FILIAL = '"+FWxFilial("PHS")+"' AND	A.PHS_IDPROC = '"+ cIDProc +"' AND A.PHS_STATUS = ' ' AND A.PHS_TPOPER = PHS.PHS_TPOPER AND A.D_E_L_E_T_ = ' ' ) AS QTDNPROC  "
		cQuery += "   FROM "+ RetSqlName("PHS") +" PHS	"						 
		cQuery += "  WHERE PHS_FILIAL = '" +FWxFilial("PHS")+"' "				 
		cQuery += "    AND PHS_IDPROC = '" + cIDProc +"'	"						 
        cQuery += "    AND PHS_TPOPER = '000024' "			
		cQuery += "    AND PHS.D_E_L_E_T_ = ' '  "									
		cQuery += " GROUP BY PHS_TPOPER	 "									 
		cQuery += " ORDER BY PHS_TPOPER	"									
		
Return cQuery


User Function GCIIA13J(nDia, cHora)  //Job inicio do Mes
    Local nqThread  := 100
    Local cChave    := "FECHAMENTOFOTO"
    Local cRotJob   := "U_GCIIA13G"  // rotina com a query
    Local cRotThr   := "U_GCIIA13T"  // rotina da ponta na thread
    Local cRotErr   := "U_GCIIA13E"
    Local cRotEnd   := "U_GCIIA13F"

    Local aPar      := {}
    Local cAMSX6    := ""    
    Local cAMProc   := ""
    Local nTimeOut  := 10
    Local oServer  := ""

    Default nDia := 0
    Default cHora := ""

    If ! Empty(nDia)
                
        RpcSetType(3)
        RpcSetEnv("00", "00001000100")   

        If day(date()) < nDia
            Return 
        EndIF 

        If Left(Time(), 5) < cHora 
            Return 
        EndIf 

    EndIf
    


    If U_JOnLine(cChave)
        Return 
    EndIf 

    cAMProc := Left(Dtos(Date()), 6)
    cAMSX6  := Alltrim(GetMV("TI_AMIIFF"))   // ANO MES INTERA ILIMITADO Fechamento Fotografia

    If cAMSX6 == cAMProc 
        Return 
    EndIf   

    cEmp := SM0->M0_CODIGO
    cFil := SM0->M0_CODFIL

    aPar := PegaParCmp(.t.)
    U_JSavePar(cChave, aPar)
    //StartJob("U_TGCVJSRV", GetEnvServer(), .F., cEmp, cFil, cChave, nqThread, cRotJob, cRotThr, cRotErr, cRotEnd)
    LoadCfg(cChave)//

    cEnvServer := __cEnvSrv
    cIP        := __cIPSrv
    cPorta     := __cPorSrv

    oServer := TRpc():New(cEnvServer)
    If ! oServer:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut,.T.)
         FreeObj(oServer)
         oServer := Nil	
        Return .F.
    EndIf
    
    __cErroP := ""
    bErroA   := ErrorBlock( { |oErro| ChkErrP( oErro ) } ) 
    Begin Sequence
        oServer:CallProc("StartJob", "U_TGCVJSRV", GetEnvServer(), .F., cEmp, cFil, cChave, nqThread, cRotJob, cRotThr, cRotErr, cRotEnd)
    End Sequence
    ErrorBlock(bErroA)	
    oServer:Disconnect()
    FreeObj(oServer)
    oServer := Nil
    PUTMV("TI_AMIIFF", cAMProc)
    
Return

Static Function ChkErrP(oErroArq)//

    If oErroArq:GenCode > 0
        __cErroP := '(' + Alltrim( Str( oErroArq:GenCode ) ) + ') : ' + AllTrim( oErroArq:Description ) + CRLF
    EndIf 

    Break
Return 

Static Function LoadCfg(cChave)
    Local cFilecfg := "system\tijob\" + cChave + ".cfg"
    Local cParam   := ""
    Local aIPs     := {}
    Local aLstIP   := {}
    Local nx       := 0
    Local nUso     := 0
    Local cEnv     := GetEnvserver()
    Local cIP      := PegaIP()  
    Local cPorta   := AllTrim(GetPvProfString("TCP", "Port", "0", GetAdv97()))

    

    If FindFunction("U_XJOBDEV")  //Para teste local em ambiente de desenvolvimento. Basta criar compilar o PE.
        aadd(aIPs, {cEnv, cIP, cPorta, 3, 0, 0, "GERENCIADOR", "Ok!"})
        aadd(aIPs, {cEnv, cIP, cPorta, 7, 0, 0,  "EXECUTOR"  , "Ok!"})
    Else
        cParam := Alltrim(MemoRead(cFilecfg))
        FWJsonDeserialize(cParam, @aIPs)
    EndIf

    For nx:= 1 to Len(aIPs)
        If aIPs[nx, 7] == "GERENCIADOR"
            __cEnvSrv := aIPs[nx, 1]


            __cIPSrv  := aIPs[nx, 2]
            __cPorSrv := aIPs[nx, 3]
    
            loop 
        EndIf
    
        If TesteIP(aIPs[nx, 1], aIPs[nx, 2], aIPs[nx, 3], @nUso)
            aIPs[nx, 5] := nUso
            aIPs[nx, 6] := Max(aIPs[nx, 4] - nUso, 0)
            aIPs[nx, 8] := "Ok!"
            aadd(aLstIP, aclone(aIPs[nx]))
        EndIf
    Next
    
Return 

Static Function TesteIP(cEnvServer, cIP, cPorta, nUso)
    Local oServer 
    Local nTimeOut := 10
    Local aInfoThr := {}
    
    nUso := 0

    If __cMyIp == Alltrim(cIP) .and. __cMyPort == cPorta
        nUso := len(GetUserInfoArray())
        Return .t.
    EndIf

    oServer := TRpc():New(cEnvServer)
    If ! oServer:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut,.T. )	
        Return .F.
    EndIf

    __cErroP := ""
    bErroA   := ErrorBlock( { |oErro| ChkErrP( oErro ) } ) 
    Begin Sequence
        aInfoThr := oServer:CallProc("GetUserInfoArray")
    End Sequence
	ErrorBlock(bErroA) 
    oServer:Disconnect()

    If ! Empty(__cErroP)
        __cErroP := ""
        Return .F.
    EndIf
    nUso := len(aInfoThr)

Return .T.



//cLstCli += "CONTEZBC7,CONTEZHSI,CONTFBKI1,CONTFBWCF"
//cLstCli += "CONTEZBC7,CONTEZHSI,CONTFBKI1,CONTFBWCF,CONT09267,CONT25106,CONTFBZBX,CONTEZJF7,CONT16081,CONT25848,CONT04721,CONTEWBTY,CONTEZMFG,"
//cLstCli += "CONTEZVHV,CONTFBZEN,CONT44023,CONTEXSOS,CONTFBXRL,CONT46988,CONT76872,CONTEXCMU,CONT09609,CONTA2801,CONTAAKAB,CONT09077,CONTEZFDP,CONTFBKED,CONT13017"


User Function GCIIA13G(cChave, aRetPar) 
    Local cChaveSrv   := "SRV_" + cChave 
    Local cTMP        := ""
    Local cAMProc     := Left(Dtos(Date()), 6)
    Local cTime       := StrTran(Time(), ":", "")
    Local cIdProc     := ""
    Local cQuery      := ""
    Local cUserId     := ""
    Local cLstCli     := ""
    Local cAniver     := ""
    Local lOk         := .T. 
    Local cTPOpeFF    := "000024"
    Local cNumCXK     := ""

    
    cUserId     := Alltrim(RetPar(aRetPar,  "cUserId"    ))
    cLstCli     := Alltrim(RetPar(aRetPar,  "cLstCli"    ))
    cAniver     := Alltrim(RetPar(aRetPar,  "cAMAnive"   ))

    Private lAbortPrint := .F.

    cIDProc := BuscaPHT(cAniver)
    If Empty(cIDProc)
        cIdProc     := Padr(DtoS(MsDate()) + cTime + "_" + StrZero(Randomize(10, 1000), 3), 18)

        FWMonitorMsg( cChaveSrv + " Executando Query ")
        U_JSaveArq(cChaveSrv + ".log", " Executando Query - Fechamento Fotografia " + Time())
        U_JMsg(cChaveSrv, "Executando Query - Fechamento Fotografia  ")

        cTMP := MontaQry1(aRetPar, @cQuery, cAniver)
        U_JSaveQry(cQuery + CRLF)

        If Empty(cTMP)
            U_JSaveArq(cChaveSrv + ".log", " Retorno de quey vazio!!! " + Time())
            U_JMsg(cChaveSrv, "Retorno de query vazio!!! ")
            lOk := .F.
        Else 
            CriaPHT(cIdProc, cAMProc, aRetPar, cTPOpeFF, cAniver)
            AtuPHTObs(cIdProc, cQuery, cTPOpeFF)
            CriaPHS(cChave, cTMP, cIdProc,  cUserId, cTPOpeFF)  
        EndIf
    Else 
        PHS->(DbSetOrder(6)) // PHS_FILIAL+PHS_IDPROC+PHS_CONTRA+PHS_REVISA+PHS_TPOPER+PHS_GRUPO+PHS_UNINEG
        cChvIDFF := FwxFilial("PHS") + cIdProc 
        PHS->(DbSeek(cChvIDFF))
        While  PHS->(! Eof()) .And. cChvIDFF == PHS->PHS_FILIAL + PHS->PHS_IDPROC 
          
            If PHS->PHS_STATUS == "2" // CONCLUIDO
                PHS->(DbSkip())
                Loop 
            EndIf 

            PHS->(RecLock("PHS"))
            PHS->PHS_STATUS := " "
            PHS->(MsUnlock())
            PHS->(DbSkip())
        End
        AtuPHTObs(cIdProc, "Reprocessado em " + dtoc(dDatabase) + " as " + Time(), cTPOpeFF)
    EndIf 

    If lOk

        ExecPHS(cChave , cIdProc, cUserId, cLstCli, cTPOpeFF, cAniver, cNumCXK)    // utilizando indice na PHS
        ExecBloq(cChave, cIdProc, cUserId, cTPOpeFF, cAniver, cNumCXK)    // utilizando indice na PHS

        U_JMsg(cChaveSrv, " Aguardando termino das threads ")
        AtuPHTObs(cIdProc, "Aguardando termino das threads ", cTPOpeFF) 
        U_JFim(cChave, cTPOpeFF)  // aguarda o fim das threads
    EndIf 
    
    FimPHT(cIdProc, cTPOpeFF)
    AtuPHTObs(cIdProc, "Finalizado as " + Time(), cTPOpeFF ) 
    
    FWMonitorMsg( cChaveSrv + " Distribuição finalizada - Aguarde o termino das threads!")
    U_JMsg(cChaveSrv, " Processamento finalizado! ")
    
Return


Static Function AtuPHTObs(cIdProc, cMsg, cTPOper)

    PHT->(DbSetOrder(1))
    If PHT->(DbSeek(xFilial("PHT") + cIdProc + cTPOper))
        PHT->(RecLock("PHT", .F.))
        PHT->PHT_OBS	:= Alltrim(PHT->PHT_OBS) + cMsg + CRLF
        PHT->(MsUnLock())
        PHT->(dbCommit())
    EndIf

Return 

Static Function CriaPHT(cIdProc, cAMProc, aRetPar, cTPOper, cAniver)
    Local cParams := ""
    Local cUserId   := __cUserId

	If aRetPar <> Nil
        cUserId     := Alltrim(RetPar(aRetPar,  "cUserId" ))
	EndIf

    
    cParams   := "Processamento: "+ AMtoCmp(cAMProc) + CRLF
    cParams   += "Processo: Fechamento Fotografia" + CRLF
    
    CriaPHTOper(cIdProc, cTPOper, cParams, cUserId, cAniver)

Return

Static Function CriaPHTOper(cIDProc, cTpOper, cParams, cUserId, cAniver)
    Local cChave    := ""
    Local cCompet   := AMtoCmp(cAniver)
    
    cChave := xFilial("PHT") + cIdProc + cTpOper
    PHT->(DbSetOrder(1))
    If ! PHT->(DbSeek(cChave))
        PHT->(RecLock("PHT", .T.))
        PHT->PHT_FILIAL := FWxFilial('PHT')
        PHT->PHT_IDPROC := cIDProc
        PHT->PHT_TPOPER := cTpOper
        PHT->PHT_ROTINA := "TGCIIA13"
        PHT->PHT_PARAMS := cParams
        PHT->PHT_STATUS := "1"
        PHT->PHT_DTINI	:= MsDate()
        PHT->PHT_HRINI	:= Time()
        PHT->PHT_OBS	:= "Iniciado" + CRLF
        PHT->PHT_QREGQR := 0
        PHT->PHT_CUSER	:= cUserId
        PHT->PHT_NUSER	:= UsrRetName(cUserId)
        PHT->PHT_COMPET := cCompet
        PHT->(MsUnLock())
        PHT->(dbCommit())
    EndIf
    
Return

Static Function CriaPHS(cChave, cTMP, cIdProc,  cUserId, cTPOper)
    Local nRecnoPQB := 0
    Local cChaveSrv   := "SRV_" + cChave 
    
    FWMonitorMsg( cChaveSrv + " Processando a distribuição Log")
    U_JSaveArq(cChaveSrv + ".log", " Processando a distribuição Log " + Time())
    U_JMsg(cChaveSrv, " Processando a distribuição Log ") 
    AtuPHTObs(cIdProc, "Processando a distribuição Log ", cTPOper) 

    While  (cTMP)->(! Eof())
        nRecnoPQB := (cTMP)->RECNO
        PQB->(DbGoto(nRecnoPQB))

        LogFF(" ", "", cIdProc, cUserId, cTPOper)

        (cTMP)->(DbSkip())
    End
    (cTMP)->(DbCloseArea())

    
Return

Static Function ExecPHS(cChave, cIdProc, cUserId, cLstCli, cTPOper, cAMAnive, cNumCXK)
    Local cChaveSrv := "SRV_" + cChave
    Local cStatus   := ""

    FWMonitorMsg( cChaveSrv + " Processando a distribuição Execução")
    U_JSaveArq(cChaveSrv + ".log", " Processando a distribuição Execução " + Time())
    U_JMsg(cChaveSrv, " Processando a distribuição Execução ")
    AtuPHTObs(cIdProc, "Processando a distribuição Execução ", cTPOper) 

    //1=Iniciado;2=Concluido;3=Erro Fatal;4=Dados Invalidos;5=Bloqueado
    cStatus := " " //Não iniciado
    FWMonitorMsg( cChaveSrv + " Distribuindo geração de Fechamento") 
    ProcPHS(cChave, cIdProc, cTpOper, cStatus, cUserId, cLstCli, cAMAnive, cNumCXK)          
	

Return


Static Function ExecBloq(cChave, cIdProc, cUserId, cTpOper, cAMAnive, cNumCXK)
    Local cChaveSrv := "SRV_" + cChave
    Local cStatus   := ""
    
    AtuPHTObs(cIdProc, "Processando a distribuição dos bloqueados ", cTpOper) 
    U_JMsg(cChaveSrv, " Aguardando execução das threads ")
    U_JWait(cChave)  // aguarda a execução de todas as threads, ficando com o status de aguardando 

    //1=Iniciado;2=Concluido;3=Erro Fatal;4=Dados Invalidos;5=Bloqueado
    cStatus := "5"
    FWMonitorMsg( cChaveSrv + " Verificando contratos Bloqueados") 
    AtuPHTObs(cIdProc, "Processando a distribuição bloqueados pos a espera ", cTpOper)                                                 
    ProcPHS(cChave, cIdProc, cTpOper, cStatus, cUserId, NIL,cAMAnive, cNumCXK)
	
Return

Static Function ProcPHS(cChave, cIdProc, cTpOper, cStatus, cUserId, cLstCli, cAMAnive, cNumCXK)
    Local cChvIDFF  := ""
    Local nRecnoPHS := 0
    Local aPar      := {}
    Local cPar      := ""
    Local cCliente  := ""

    Default cLstCli := ""



    PHS->(DbSetOrder(11)) // PHS_FILIAL+PHS_IDPROC+PHS_TPOPER+PHS_STATUS+PHS_CONTRA+PHS_NUMERO                                                                                                                                                                                                                  
    cChvIDFF := FwxFilial("PHS") + cIdProc + cTpOper + cStatus

    AtuPHTObs(cIdProc, "Inicio da distribuição laço principal..", cTpOper) 
    PHS->(DbSeek(cChvIDFF))
    While  PHS->(! Eof()  .And. cChvIDFF == PHS_FILIAL + PHS_IDPROC + PHS_TPOPER + PHS_STATUS ) 
        nRecnoPHS := PHS->(Recno())
        cCliente  := PHS->PHS_CLIENT
        
        If U_TGCVJEXIT(cChave)
            Return 
        EndIf

        If ! Empty(cLstCli) .and. ! cCliente $ cLstCli
            PHS->(DbSkip())
            Loop 
        EndIf 
        
        aPar := { nRecnoPHS, cIdProc, cUserId, cAMAnive, cNumCXK}
        cPar := FwJsonSerialize(aPar)

        // distribui o conteudo do parametro para a proxima thread disponivel
        If ! U_TGCVJDIS(cChave, cPar, /*80*/)
            // tenta novamente
            Sleep(100)
            Loop
        EndIf
       
        PHS->(DbSkip())
    End
    AtuPHTObs(cIdProc, "Termino da distribuição laço principal..", cTpOper) 

	
Return


Static Function FimPHT(cIdProc, cTpOper)
    
    PHT->(DbSetOrder(1))
    If PHT->(DbSeek(xFilial("PHT") + cIdProc + cTpOper )) 
        PHT->(U_GVParcPHT(PHT->PHT_IDPROC,,, cTpOper))
        PHT->(RecLock("PHT", .F.))
            If Empty(PHT->PHT_QINVAV) .and. Empty(PHT->PHT_QERRO) .and. Empty(PHT->PHT_QINICI) .and. Empty(PHT->PHT_QNPROC) .and. Empty(PHT->PHT_QBLOQU)
                PHT->PHT_STATUS := "2" // com sucesso
                PHT->PHT_OBS	:= AllTrim(PHT->PHT_OBS) + "Finalizado com sucesso" + CRLF
            Else
                PHT->PHT_STATUS := "3" // com falha
                PHT->PHT_OBS	:= AllTrim(PHT->PHT_OBS) + "Finalizado com ocorrencia" + CRLF
            EndIf
            PHT->PHT_DTFIM	:= MsDate()		 
            PHT->PHT_HRFIM	:= Time()
            PHT->PHT_TIMEPR := ElapTime( PHT->PHT_HRINI, PHT->PHT_HRFIM ) 
        PHT->(MsUnLock())
        PHT->(dbCommit())
    EndIf
 
Return

static __nSeq := 0
User Function GCIIA13T(cChaveTHR, cDir, cPar)
    
    Local cIdProc     := ""
    Local cUserId     := ""
    
    Local cRetorno    := ""
    Local cMsgErro    := ""
    Local nRecPHS     := 0 
    Local aPar        := {}
    Local cAMAnive    := ""
    Local cNumCXK     := ""
   
    FWJsonDeserialize(cPar, @aPar)

    nRecPHS    := aPar[1]
    cIdProc    := aPar[2]
    cUserId    := aPar[3]
    cAMAnive   := aPar[4]
    cNumCXK    := aPar[5]
    
    __cUserId := cUserId

    cRetorno := "   Processando solicitação [" + Alltrim(Str(++__nSeq)) + "] " + " Geração " + Time() 
    cMsgErro := U_GCIIA13A(nRecPHS, cIdProc, cAMAnive, cNumCXK)
    If ! Empty(cMsgErro) 
        cRetorno += CRLF+ "      ATENÇÃO: " + cMsgErro   
    Else 
         cRetorno += " OK!"
    EndIf  


Return cRetorno

User Function GCIIA13P(cChave, lJob)
	Local aRetPar := {}
    
    aRetPar := PegaParCmp(lJob) 


Return aclone(aRetPar)



User Function GCIIA13E(cChave, cPar, cErro)
    Local cIdProc     := ""
    Local aPar        := {}
    
    If InTransact()
	    DisarmTransaction()
    Endif
    
    If SELECT("PHS") == 0
        CHKFILE("PHS")
    EndIf
    FWJsonDeserialize(cPar, @aPar)


    nRecPHS    := aPar[1]
    cIdProc    := aPar[2]

    Begin Transaction
        PHS->(DbGoto(nRecPHS))
        PHS->(RecLock("PHS", .F.))
        PHS->PHS_STATUS := "3"
        PHS->PHS_OBS	:= Alltrim(PHS->PHS_OBS) + cErro + CRLF
        PHS->PHS_HRFIM	:= Time()
        PHS->PHS_DTFIM	:= MsDate()
        PHS->(MsUnLock())

    End Transaction
    
    cErro += "Atualizado arquivo de log PHS " + CRLF
    cErro += "ID de Processamento: " + cIDProc + CRLF

Return cErro


User Function GCIIA13F(cChave, cAniver)
    Local cMailTo	 := GetMV("TI_IINCEMAI" ,, "<EMAIL>")
    Local cTitMail   := ""
    Local cTextMail  := ""
    Local lAutentica := GetMv("MV_RELAUTH" ,, .F.)
    Local cIdProc    := ""
    Local cChavePHT  := ""
    Local cCliente   := ""
    Local cStatus    := ""
    Local cObs       := ""
    Local aRetPar    := {}
    


    If Empty(cMailTo)
        Return 
    EndIf

    cTitMail := "Job Fechamento Fotografia - Finalizado"

    If cAniver == NIL 
        aRetPar  := U_JLoadPar(cChave)
        cAniver  := Alltrim(RetPar(aRetPar,  "cAMAnive"   ))
    EndIf 
 
    cIdProc := BuscaPHT(cAniver)
    If Empty(cIdProc)
        Return 
    EndIf 
   
    PHT->(U_GVParcPHT(cIdProc,,, "000024"))

    cChavePHT := xFilial("PHT") + cIdProc + "000024"
    PHT->(DbSetOrder(1))
    If ! PHT->(DbSeek(cChavePHT))
        Return 
    EndIf 

    cTextMail += "<font size=3 face=calibri>  " + CRLF
	cTextMail += "   <table width=100% border=0> " + CRLF
 	cTextMail += "   </table>" + CRLF
    
    cTextMail += MontaEmail()


    cTextMail += "   <table border=1 cellspacing=0 cellpadding=2 bordercolor='666633'>" + CRLF
    cTextMail += "      <tr style='background-color:#CDC9C9'>" + CRLF
	cTextMail += "         <td width='140'><b><font COLOR='BLUE'>Cliente </font></b></td>" + CRLF
	cTextMail += "         <td width='240'><b><font COLOR='BLUE'>Status  </font></b></td>" + CRLF
    cTextMail += "         <td width='240'><b><font COLOR='BLUE'>Observação</font></b></td>" + CRLF
	cTextMail += "      </tr>" + CRLF
    
    

    PHS->(DbSetOrder(11)) // PHS_FILIAL+PHS_IDPROC+PHS_TPOPER+PHS_STATUS+PHS_CONTRA+PHS_NUMERO                                                                                                                                                                                                                  
    PHS->(DbSeek(FwxFilial("PHS") + cIdProc))
    While  PHS->(! Eof()  .And. FwxFilial("PHS") + cIdProc == PHS_FILIAL + PHS_IDPROC) 

        cCliente := PHS->PHS_CLIENT
        If PHS->PHS_STATUS == " "
            cStatus  := "Não Iniciado"
        ElseIf PHS->PHS_STATUS == "1"
            cStatus  := "Iniciado"
        ElseIf PHS->PHS_STATUS == "2"
            cStatus  := "Finalizado"
        ElseIf PHS->PHS_STATUS == "3"
            cStatus  := "Erro de programa"
        ElseIf PHS->PHS_STATUS == "4"
            cStatus  := "Ocorrencia"
        ElseIf PHS->PHS_STATUS == "5"
            cStatus  := "Bloqueado"            
        EndIf 
        cObs     := PHS->PHS_OBS
        
        cTextMail += "      <tr>" + CRLF
        cTextMail += "         <td width='140'><b>" + cCliente + "</b></td>" + CRLF
        If PHS->PHS_STATUS $ "1345"
            cTextMail += "         <td width='240'><b><font COLOR='RED'>" + cStatus  + "</font></b></td>" + CRLF
        Else 
            cTextMail += "         <td width='240'><b>" + cStatus  + "</b></td>" + CRLF
        EndIf 
        cTextMail += "         <td width='240'><b>" + cObs      + "</b></td>" + CRLF
        cTextMail += "      </tr> "
        
        PHS->(DbSkip())
    End
    cTextMail += "   </table>" + CRLF

	cTextMail += "<hr>" + CRLF
	cTextMail += "<br>" + CRLF
 

    U_xSendMail(cMailTo, cTitMail, cTextMail, , .T., , , lAutentica, .T.)
    

Return 

Static Function MontaEmail()
    Local cId      := PHT->PHT_IDPROC
    Local nQContra := PHT->PHT_QREGQR 
    Local nQConclu := PHT->PHT_QCONCL 
    Local nQInvali := PHT->PHT_QINVAV 
    Local nQBloque := PHT->PHT_QBLOQU
    Local cStatus  := PHT->PHT_STATUS
    Local dDtIni   := PHT->PHT_DTINI
    Local cHrIni   := PHT->PHT_HRINI
    Local dDtFim   := PHT->PHT_DTFIM
    Local cHrFim   := PHT->PHT_HRFIM
    Local cTempo   := PHT->PHT_TIMEPR
    Local cDesSta  := "Iniciado"
    Local cHtml    := ""

    If cStatus == "2"
        cDesSta  := "Finalizado"
    ElseIf cStatus == "3"
        cDesSta  := "Finalizado com ocorrencias"
    EndIf  

    cHtml += "   <table border=1 cellspacing=0 cellpadding=2 bordercolor='666633'>" + CRLF
    cHtml += "      <tr style='background-color:#CDC9C9'>" + CRLF
	cHtml += "         <td width='140'><b><font COLOR='BLUE'> Operação </font></b></td>" + CRLF
 
	cHtml += "         <td width='240'><b><font COLOR='BLUE'>Fechamento Fotografia</font></b></td>" + CRLF
  
	cHtml += "      </tr>" + CRLF

    cHtml += "      <tr>" + CRLF
	cHtml += "         <td><b>ID</b></td>" + CRLF
	cHtml += "         <td> " + cID  + " </td>" + CRLF
    cHtml += "      </tr>" + CRLF

    cHtml += "      <tr>" + CRLF
	cHtml += "         <td><b>Inicio</b></td>" + CRLF
	cHtml += "         <td> " + Dtoc(dDtIni) + " " + cHrIni + " </td>" + CRLF
    cHtml += "      </tr>" + CRLF

    cHtml += "      <tr>" + CRLF
	cHtml += "         <td><b>Termino</b></td>" + CRLF
	cHtml += "         <td> " + Dtoc(dDtFim) + " " + cHrFim + " </td>" + CRLF
    cHtml += "      </tr>" + CRLF

    cHtml += "      <tr>" + CRLF
	cHtml += "         <td><b>Duração</b></td>" + CRLF
	cHtml += "         <td> " + cTempo + " </td>" + CRLF
    cHtml += "      </tr>" + CRLF

    cHtml += "      <tr>" + CRLF
	cHtml += "         <td><b>Status</b></td>" + CRLF
	cHtml += "         <td> " + cDesSta + " </td>" + CRLF
    cHtml += "      </tr>" + CRLF

    cHtml += "      <tr>" + CRLF
	cHtml += "         <td><b>Contratos </b></td>" + CRLF
	cHtml += "         <td> " + cValtoChar(nQContra) + " </td>" + CRLF
    cHtml += "      </tr>" + CRLF

    cHtml += "      <tr>" + CRLF
	cHtml += "         <td><b>Concluido </b></td>" + CRLF
	cHtml += "         <td> " + cValtoChar(nQConclu) + " </td>" + CRLF
    cHtml += "      </tr>" + CRLF

    cHtml += "      <tr>" + CRLF
	cHtml += "         <td><b>Ocorrencias </b></td>" + CRLF
	cHtml += "         <td> " + cValtoChar(nQInvali) + " </td>" + CRLF
    cHtml += "      </tr>" + CRLF

    cHtml += "      <tr>" + CRLF
	cHtml += "         <td><b>Bloqueados </b></td>" + CRLF
	cHtml += "         <td> " + cValtoChar(nQBloque) + " </td>" + CRLF
    cHtml += "      </tr>" + CRLF

    cHtml += "   </table>" + CRLF
	cHtml += "<hr>" + CRLF
	cHtml += "<br>" + CRLF


Return cHtml

/*
===========================================================================================================
==   funcoes auxiliares
===========================================================================================================
*/




User Function GCIIA13A(nRecPHS, cIdProc, cAMAnive, cNumCXK)
	Local aArea       := GetArea()
    Local aAreaSA1    := SA1->(GetArea())
    Local aAreaCN9    := CN9->(GetArea())
	Local cMsgErro    := ""
	Local cChaveLock  := ""
	Local cMsgLock    := ""
	Local cCliente    := ""
    Local cContrato   := ""
    Local cTPOper     := "000024"
    
	Default cIdProc   := ""
	
	PRIVATE lMSERROAUTO := .F.
	PRIVATE lMSHELPAUTO	:= .T.

    PHS->(DbGoto(nRecPHS))
    nRecPQB := PHS->PHS_RECORI
    PQB->(DbGoto(nRecPQB))

    cContrato := PHS->PHS_CONTRA 
    cCliente  := PHS->PHS_CLIENT

    LogFF("1", "", cIdProc, __cUserId, cTPOper)


	SA1->(DbSetOrder(1))
	If ! SA1->(DbSeek(xFilial("SA1") + cCliente ))
		cMsgErro := "Cliente " + cCliente + " não cadastrado!"
		LogFF("4", cMsgErro, cIdProc,  __cUserId, cTPOper)

        RestArea(aAreaSA1)
        RestArea(aAreaCN9)
        RestArea(aArea)
		Return cMsgErro
	EndIf 

	If ! SA1->(RLock())
		cMsgErro := "Cliente " + cCliente + " em uso (registro bloqueado) em outra estação!"
		LogFF("5", cMsgErro, cIdProc,  __cUserId, cTPOper)
        RestArea(aAreaSA1)
        RestArea(aAreaCN9)
        RestArea(aArea)
		Return cMsgErro
	EndIf 


	CN9->(DBOrderNickname("CN9P03"))
	CN9->(DbSeek(FwxFilial("CN9") + "05" + "2" + "013" + AllTrim(cContrato)))
	
    cChaveLock := "CN9" + xFilial("CN9") + PHS->PHS_CONTRA
    
	If ! U_GVFUNLOC(1, cChaveLock, , .T.)
		cMsgLock := AllTrim(PH6->PH6_CONTRA) + " - Contrato em uso por outro usuário -> " + AllTrim(PHW->PHW_NUSER)
		LogFF("5", cMsgLock, cIdProc, __cUserId, cTPOper)
	
    	RestArea(aAreaSA1)
        RestArea(aAreaCN9)
        RestArea(aArea)

		Return cMsgLock	 
	EndIf

    cMsgErro := U_GCIIA11R(cCliente, cAMAnive , cNumCXK)

	If Left(cMsgErro, 2) == "OK"
        LogFF("2", cMsgErro, cIdProc, __cUserId, cTPOper)
    ElseIf ! Empty(cMsgErro)
		LogFF("4", cMsgErro, cIdProc,  __cUserId, cTPOper)
	Else
		LogFF("2", "Fechamento Fotografia concluido", cIdProc, __cUserId, cTPOper)
	EndIf	

	U_GVFUNLOC(2, cChaveLock)  // Destrava o contrato
    SA1->(MsUnlock())

    RestArea(aAreaSA1)
    RestArea(aAreaCN9)
    RestArea(aArea)


Return cMsgErro	


User Function GCIIA13L(cStatus, cObs, cIdProc, cUserId, cTPOper) 

Return LogFF(cStatus, cObs, cIdProc, cUserId, cTPOper)               // retorna o id 



Static Function LogFF(cStatus, cObs, cIdProc, cUserId, cTPOper)
	Local aAreaPHS:= PHS->(GetArea())
    Local cParams := ""
	Local cCmp    := AMtoCmp(Left(Dtos(dDataBase), 6))

	
	
    cParams   := "Processamento: "+ cCmp + CRLF
    cParams   += "Processo: Geração de Fechamento Fotografia" + CRLF
	
    If ! Empty(cIDProc)
		LogPHSOper(cIDProc, cTpOper, cStatus, cObs, cUserId)
    EndIf

	RestArea(aAreaPHS)

Return

/*/ {Protheus.doc} LogPHSOper
Rotina para atualizar o Log 
<AUTHOR> SANDRO VALARIO
@since   	13/03/2022
/*/

Static Function LogPHSOper(cIDProc, cTpOper, cStatus, cObs, cUserId)
	Local cCliente  := PQB->PQB_CODCLI
    Local cContrato := Padr("CON" + PQB->PQB_CODCLI, 15)
    Local cCompet   := AMtoCmp(PQB->PQB_ANIVER)
	Local nRecno    := PQB->(Recno())
	
    If cStatus == " "
        PHS->(RecLock("PHS", .T.))
        PHS->PHS_FILIAL := xFilial('PHS')
        PHS->PHS_IDPROC := cIDProc
        PHS->PHS_GRUPO	:= cEmpAnt
        PHS->PHS_UNINEG := FWxFilial("CN9")
        PHS->PHS_CONTRA := cContrato
        PHS->PHS_CLIENT := cCliente 
        PHS->PHS_TPOPER := cTpOper
        PHS->PHS_COMPET := cCompet
        PHS->PHS_HRINI	:= Time()
        PHS->PHS_DTINI	:= MsDate()
        PHS->PHS_CUSER	:= cUserId
        PHS->PHS_NUSER	:= UsrRetName(cUserId) 
        PHS->PHS_STATUS := " " 
        PHS->PHS_TABORI := "PQB"
        PHS->PHS_RECORI := nRecno 
        PHS->(MsUnLock())
    Else
        If PHS->PHS_STATUS == "3" // ERRO DE PROGRAMA
            Return
        EndIf
        While ! PHS->(rLock())
            // aguardar o resgistro se destravado por outra thread
            PHS->(DbSkip(0))
            Sleep(100)
        End 
        
        If Empty(PHS->PHS_IP)
            PHS->PHS_THREAD := Alltrim(Str(ThreadId()))
            PHS->PHS_IP     := PegaIP()
            PHS->PHS_PORTA  := GetServerPort()
        EndIf
        

		PHS->PHS_RECORI := nRecno
        If PHS->PHS_STATUS == "5" .or. cStatus > PHS->PHS_STATUS
            PHS->PHS_STATUS := cStatus
        EndIf
        If ! Empty(cObs) 
            cObs += CRLF
        EndIf
        PHS->PHS_OBS	:= Alltrim(PHS->PHS_OBS) + cObs
        If ! cStatus $ " 15"
            PHS->PHS_HRFIM	:= Time()
            PHS->PHS_DTFIM	:= MsDate()
            PHS->PHS_CUSER	:= cUserId
            PHS->PHS_NUSER	:= UsrRetName(cUserId)             
        EndIf
        PHS->(MsUnLock())
    EndIf
    PHS->(DbCommit())
Return

Static Function PegaIP()
    Local cIP := ""
    Local aIP := GetServerIP(.T.)  // aqui retorna um array com os ips da maquina
    Local nx  
    
    For nx := 1 to Len(aIP)
        If Left(aIP[nx, 4], 3) == "172" 
            cIP := aIP[nx, 4]
        EndIf
    Next
    If Empty(cIP)
        cIP := GetServerIP(.F.)  // retorna o ip da conexão
    EndIf 
    If Empty(cIP)
        cIP := "*********"  // Localhost
    EndIf 

Return cIP


