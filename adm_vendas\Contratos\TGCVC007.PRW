#INCLUDE "TGCVC007.ch"
#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "FWBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TOPCONN.CH"

//-------------------------------------------------------------------
/*/{Protheus.doc} TGCVC007
Consulta de Threads usadas no processamento em Grid ( Lotes ) na 
manutenção / Medição de Venda do contrato

<AUTHOR>
@since 01/04/2016
@version 1.0
@param cIDProc, character, (Descrição do parâmetro)
@return ${return}, ${return_description}
/*/
//-------------------------------------------------------------------
User Function TGCVC007(cIDProc ) 
	
	Local aCoors 		:= FWGetDialogSize(oMainWnd)
	Local cTitle		:= STR0001 //"Threads de Processamento de Lotes"
	Private oBrowsePHU	:= NIL
	Private oDlg		:= NIL
	
	Default cIDProc		:= ""

    If Alltrim(PHT->PHT_ROTINA) == "TGCVA102M"
        U_GCVA102M()      
        Return 
    EndIf

	If Alltrim(PHT->PHT_ROTINA) == "TGCVA121M"
        U_GCVA121M()       
        Return 
    EndIf
	
	If Alltrim(PHT->PHT_ROTINA) == "TGCVA144M"
        U_GCVA144M()       
        Return 
    EndIf

	If Alltrim(PHT->PHT_ROTINA) == "TGCVA146M"
        U_GCVA146M()       
        Return 
    EndIf

	If Alltrim(PHT->PHT_ROTINA) == "TFATA461C"
        U_FATA461M()       
        Return 
    EndIf
	
	If Alltrim(PHT->PHT_ROTINA) == "TGCIIA12"
        U_GCIIA12M()   //TIADMVIN-3138      
        Return 
    EndIf

	If Alltrim(PHT->PHT_ROTINA) == "TGCIIA13"
        U_GCIIA13M()   
        Return 
    EndIf

	If Alltrim(PHT->PHT_ROTINA) == "TGCVXC23"
        U_GCIIA15M()   
        Return 
    EndIf
	
	dbSelectArea('PHU')
	PHU->(dbSetOrder(1))
	
	DEFINE MSDIALOG oDlg Title cTitle From aCoors[1], aCoors[2] To aCoors[3], aCoors[4] PIXEL
	
		oBrowsePHU:= FWMBrowse():New()
		oBrowsePHU:SetOwner(oDlg)
		oBrowsePHU:SetDescription(STR0001)  //"Threads de Processamento de Lotes"
		oBrowsePHU:SetMenuDef('TGCVC007')
		oBrowsePHU:SetAlias('PHU')
		oBrowsePHU:Alias('PHU')
		oBrowsePHU:ForceQuitButton()
		If ! Empty(cIDProc) 
			oBrowsePHU:SetFilterDefault(U_GVC07FIL(cIDProc ))
		EndIf
		
		oBrowsePHU:AddLegend("Alltrim(PHU->PHU_STATUS) == '0'"	, 'WHITE'	, STR0002		)  //"Aguardando Inicio"
		oBrowsePHU:AddLegend("Alltrim(PHU->PHU_STATUS) == '1'"	, 'YELLOW'	, STR0003		)  //"Iniciado"
		oBrowsePHU:AddLegend("Alltrim(PHU->PHU_STATUS) == '2'"	, 'GREEN'	, STR0004		)  //"Concluido Sucesso"
		oBrowsePHU:AddLegend("Alltrim(PHU->PHU_STATUS) == '3'"	, 'GRAY'	, STR0005		) //"Finalizado User"
		oBrowsePHU:AddLegend("Alltrim(PHU->PHU_STATUS) == '9'"	, 'RED'		, STR0019		) //"Erro Fatal"
		oBrowsePHU:Activate()

	ACTIVATE MSDIALOG oDlg CENTER
	
Return

//-------------------------------------------------------------------
/*/{Protheus.doc} MenuDef
Opções de Menu da rotina
<AUTHOR>
@since 01/04/2016
@version 1.0
/*/
//-------------------------------------------------------------------
Static Function MenuDef()
	
	Local aRotina		:= {}
	
	ADD OPTION aRotina TITLE STR0006 		ACTION "VIEWDEF.TGCVC007"	OPERATION MODEL_OPERATION_VIEW ACCESS 0 //"Visualizar"
	ADD OPTION aRotina TITLE STR0007 		ACTION "U_TGCVC07M(1)"  	OPERATION MODEL_OPERATION_VIEW ACCESS 0 //"Finalizar Thread"
	ADD OPTION aRotina TITLE STR0008 		ACTION "U_TGCVC07M(2)"  	OPERATION MODEL_OPERATION_VIEW ACCESS 0 //"Finalizar Processo"
	ADD OPTION aRotina TITLE STR0009 		ACTION "U_TGCVC07E"  		OPERATION MODEL_OPERATION_VIEW ACCESS 0 //"Eliminar Pendencias"
	
Return aRotina

//-------------------------------------------------------------------
/*/{Protheus.doc} ModelDef
Modelo para apresentar os dados do registro

<AUTHOR>
@since 31/03/2016
@version 1.0
/*/
//-------------------------------------------------------------------
Static Function ModelDef()
	
	Local oStruPHS	:= FWFormStruct(1,'PHU')
	Local oModel	:= MPFormModel():New( 'GVC07MVC',,, )
	
	oModel:SetDescription(STR0010) //"Threads de Processamento em Lote"
	
	oModel:AddFields( 'PHUMASTER', , oStruPHS )
	
	oModel:SetPrimaryKey({"PHU_IDPROC"})

Return oModel


//-------------------------------------------------------------------
/*/{Protheus.doc} ViewDef
Tela para apresentar os dados do registro

<AUTHOR>
@since 31/03/2016
@version 1.0
/*/
//-------------------------------------------------------------------
Static Function ViewDef()
	
	Local oModel		:= FWLoadModel("TGCVC007")
	Local oView			:= FWFormView():New()
	Local oStruPHU		:= Nil
	
	oStruPHU		:= FWFormStruct(2,'PHU' )
	
	oView:SetModel( oModel )
	
	oView:AddField('VIEW_PHU'	,oStruPHU	,'PHUMASTER')
	
	oView:CreateHorizontalBox('CABEC'	,100)
	
	oView:SetOwnerView('VIEW_PHU'		,'CABEC' )
	
	oView:SetCloseOnOk({||.T.})

Return oView

//-------------------------------------------------------------------
/*/{Protheus.doc} TGCVC07M
Prepara em array as Threads que devem ser encerradas

<AUTHOR>
@since 01/04/2016
@version 1.0
@param nOpc, numérico, (Descrição do parâmetro)
@return ${return}, ${return_description}
/*/
//-------------------------------------------------------------------
User Function TGCVC07M(nOpc)

	Local aThreds	:= {}
	Local nPs		:= 0
	Local cIDProc 	:= PHU->PHU_IDPROC
	Local aAreaPHU	:= PHU->(GetArea())
	
	If nOpc == 1
	
		If Alltrim(PHU->PHU_STATUS) == "1"
			aadd(aThreds, {	Alltrim(PHU->PHU_SERVID),;
							PHU->PHU_PORTA 			,;
							Alltrim(PHU->PHU_AMBIEN),;
							{{Alltrim(PHU->PHU_VARGBL) , PHU->(Recno())}  }} )
		Else
			Help(" ",1, 'Help','TGCVC07M_01', "Só é permitido finalizar threads que já foram Iniciadas.", 3, 0 )
		EndIf
		
	Else
	
		PHU->(dbGoTop())
		If PHU->(dbSeek(FWxFilial('PHU') + Padr( cIDProc, TamSx3('PHU_IDPROC')[1]) ))
			
			While PHU->(! Eof()) .And. PHU->( PHU_FILIAL + PHU_IDPROC ) == FWxFilial('PHU') + Padr( cIDProc, TamSx3('PHU_IDPROC')[1])
			
				If Alltrim(PHU->PHU_STATUS) == "1"

					If ( nPs := Ascan(aThreds, { |x| x[1] + Alltrim(Str(x[2])) + x[3]  == Alltrim(PHU->PHU_SERVID) + Alltrim(Str(PHU->PHU_PORTA)) + Alltrim(PHU->PHU_AMBIEN)  } ) ) == 0
	
						aadd(aThreds, {	Alltrim(PHU->PHU_SERVID),;
										PHU->PHU_PORTA 			,;
										Alltrim(PHU->PHU_AMBIEN),;
										{ {Alltrim(PHU->PHU_VARGBL) , PHU->(Recno())}  } } )
					Else
					
						aadd(aThreds[nPs][4], {Alltrim(PHU->PHU_VARGBL) , PHU->(Recno())  } )
						
					EndIf
					
				EndIf
														 
				PHU->(dbSkip())
			EndDo
			 
		EndIf
		
	EndIf
	
	FWMsgRun( ,{|| U_GVC7CLEAR(aThreds) },STR0011,STR0018) //"Aguarde" //"Processando..."
	
	RestArea(aAreaPHU)
	
Return

//-------------------------------------------------------------------
/*/{Protheus.doc} GVC7CLEAR
Faz a conexão RPCConet na porta onde foi realizado o processamento
e Envia a solicitação para Limpar a variavel de controle ( Global )
e atualiza o status de thread

<AUTHOR>
@since 01/04/2016
@version 1.0
@param aThreds, array, (Descrição do parâmetro)
@return ${return}, ${return_description}
/*/
//-------------------------------------------------------------------
User Function GVC7CLEAR(aThreds)
	
	Local nMaxTent	:= 3 // Qtde de tentativas de conexao RPC
	Local nTentAtu	:= 1
	Local lConnect	:= .F.
	Local oServer	:= Nil
	Local aAuxThr	:= {}
	Local aAuxRecns	:= {}
	Local nC		:= 0
	Local nI		:= 0
	Local cSQL		:= ""
	Local cNomThrd	:= ""
	Local cComputer	:= ""
	Local lExecKill	:= 	GetMv( "TI_KILLGRD",,.T.)
	
	Local aDdsThread:= {}
	
	If Len(aThreds)  > 0
		
		For nC	:= 1 To Len(aThreds)
			
			lConnect := .F.
			nTentAtu := 1
			If Valtype(oServer) == "O"
				FreeObj(oServer)
			EndIF
			oServer := Nil

			oServer	:= TRpc():New(aThreds[nC][3])
					
			If ValType(oServer) == "O"
				
				While 	!lConnect .And. nTentAtu <= nMaxTent
						
					lConnect	:= oServer:Connect( Alltrim(aThreds[nC][1]) , aThreds[nC][2] , 10 )  
					nTentAtu++
					
				EndDo
				
			EndIf

			If 	lConnect
				
				//Atualiza Variaveis Global
				aAuxThr := {}
				aEval( aThreds[nC][4] ,{|x| aadd(aAuxThr,x[1]) })

				//Recnos
				aAuxRecns := {}
				aEval( aThreds[nC][4] ,{|x| aadd(aAuxRecns,x[2]) })
				
				aDdsThread := {}
				
				If lExecKill
					
					For nI := 1 To Len(aAuxRecns)
						
						PHU->( dbGoTo( aAuxRecns[nI] ) )
	
						If PHU->(FieldPos( 'PHU_COMPUT' )) > 0 .And. PHU->(FieldPos( 'PHU_NOMTHR' )) > 0
							cNomThrd	:= Alltrim(PHU->PHU_NOMTHR)
							cComputer	:= Alltrim(PHU->PHU_COMPUT)
							aadd(aDdsThread , {PHU->PHU_THREAD , Alltrim(PHU->PHU_VARGBL) ,  Alltrim(PHU->PHU_AMBIEN) , cComputer , cNomThrd } )
						Else
							aadd(aDdsThread , {PHU->PHU_THREAD , Alltrim(PHU->PHU_VARGBL) ,  Alltrim(PHU->PHU_AMBIEN) } )
						EndIf
	
					Next nI
					
				EndIf
				oServer:CallProc("U_GC07SVGB",  { aAuxThr , aDdsThread }  )
				oServer:Disconnect()

				//  Atualiza tabela fisica
				For nI := 1 To Len(aAuxRecns)
					
					PHU->( dbGoTo(aAuxRecns[nI]) )
					
					If PHU->PHU_STATUS == '1'
					
						cSQL := " UPDATE " + RetSqlName("PHU") + " SET " 
						cSQL += " PHU_STATUS = '3' " 
						cSQL += " ,PHU_OBS = (RAWTOHEX (UTL_RAW.cast_to_raw ('"+ PHU->PHU_OBS + CRLF + "Encerrado pelo Usuário." +" .' ))) "
	        			cSQL += " WHERE  R_E_C_N_O_ = " + Alltrim(Str(aAuxRecns[nI]))+" AND D_E_L_E_T_ = ' ' " 
						
						If TCSQLExec(cSQL) <> 0
							TcSQLExec( 'ROLLBACK' )
						Else
							TcSQLExec( 'COMMIT' )
						EndIf
						
					EndIf
					
				Next nI

			EndIf
			
		Next nC
	
	Else
		Help(" ",1, 'Help','TGCVC07M', STR0013, 3, 0 ) //"Não foi localizado threads que devam ser encerradas"
	EndIf
	
Return

//-------------------------------------------------------------------
/*/{Protheus.doc} GC07SVGB
Limpa a Variavel global de acordo com a porta conectada

<AUTHOR>
@since 01/04/2016
@version 1.0
@param aJobAux, array, (Descrição do parâmetro)
@return ${return}, ${return_description}
/*/
//-------------------------------------------------------------------
User Function GC07SVGB( aClear )

	Local cJobAux 	:= ""
	Local nC		:= 0
	Local aJobAux	:= aClone(aClear[1])
	Local aDdsThread:= aClone(aClear[2])
	
	If Len(aDdsThread) > 0
		
		For nC := 1 To Len(aDdsThread)
			
			U_GVKTRGRD( aDdsThread[nC] )
					
		Next nC
		
	EndIf
	
	For nC := 1 To Len(aJobAux)
			
		cJobAux := aJobAux[nC]
		
		If GetGlbValue( cJobAux ) $ '0|1'
			PutGlbValue(cJobAux,"9")
			GlbUnlock()
		EndIf
				
	Next nC
		
Return

//-------------------------------------------------------------------
/*/{Protheus.doc} GVC07FIL
Filtro do Browse da rotina de manutenção das threads

<AUTHOR>
@since 01/04/2016
@version 1.0
@param cIDProc, character, (Descrição do parâmetro)
@return ${return}, ${return_description}
/*/
//-------------------------------------------------------------------
User Function GVC07FIL(cIDProc)

	Local cFiltro := ""
	
	Default cIDProc := ""
	
	cFiltro := "@PHU_FILIAL = '" +FWxFilial("PHU")+"' "
	cFiltro += " AND PHU_IDPROC = '"+ cIDProc +"' "

Return cFiltro

//-------------------------------------------------------------------
/*/{Protheus.doc} TGCVC07E
Chama a rotina para eliminar threads pendentes
<AUTHOR>
@since 01/04/2016
@version 1.0
@return ${return}, ${return_description}
/*/
//-------------------------------------------------------------------
User Function TGCVC07E()

	If Aviso("TGCVC07E-01",STR0014+ DtoC(MsDate()-2) +STR0015,{STR0016,STR0017},3) == 1 //"Serão elimidos todas as pendencias até a data "###", deseja continuar?"###"Sim"###"Não"
		
		FWMsgRun( ,{|| U_GVC7ELM(.F.) },STR0011,STR0018)   //"Aguarde"###"Processando..."
	EndIf
		
Return

//-------------------------------------------------------------------
/*/{Protheus.doc} GVC7Elm
Filtra as Threads pendentes de processamento, e eliminar, para
não ficar Variaveis globais presas
 
<AUTHOR>
@since 01/04/2016
@version 1.0
@param lJob, Booleam, true - Via job
@param aEmpFil, array, Empresa e Filial
/*/
//-------------------------------------------------------------------
User Function GVC7ELM(lJob,aEmpFil)

	Local lRet		:= .T.
	Local cSql		:= ""
	Local clAlias	:= ""
	Local aThreds	:= {}
	Local nPs		:= 0
	
	Default lJob 	:= .T.
	Default aEmpFil	:= {"00","00001000100"}
	
	If lJob
		lRet := U_GVJBPAMB( aEmpFil )
	EndIf
	
	If lRet
		
		cSql := " SELECT "
		cSql += " PHU_IDPROC "
		cSql += " ,PHU_TPOPER "
		cSql += " ,PHU_THREAD "
		cSql += " ,PHU_SERVID "
		cSql += " ,PHU_PORTA "
		cSql += " ,PHU_AMBIEN "
		cSql += " ,PHU_VARGBL "
		cSql += " ,PHU.R_E_C_N_O_ AS RECNOPHU "
		
		cSql += " FROM "+ RetSqlName("PHU") + " PHU "
		cSql += " WHERE PHU_FILIAL = '"+FWxFilial("PHU")+"'"
		cSql += " AND PHU.PHU_DTINI <= '" + DtoS(MsDate()-2)+ "'"
		cSql += " AND PHU.PHU_STATUS IN ('0','1')
		cSql += " AND PHU.D_E_L_E_T_= ' ' "
		cSql += " ORDER BY PHU_SERVID , PHU_PORTA  ,PHU_AMBIEN "
	
		cSql := ChangeQuery(cSql)
		clAlias := GetNextAlias()
		
		dbUseArea( .T., __cRdd, TcGenQry( ,, cSql ), clAlias, .T., .F. )
		(clAlias)->(dbGoTop())
	
		If (clAlias)->(! Eof())
			While (clAlias)->(! Eof())
	
				If ( nPs := Ascan(aThreds, { |x| x[1] + Alltrim(Str(x[2])) + x[3]  == Alltrim((clAlias)->PHU_SERVID) + Alltrim(Str((clAlias)->PHU_PORTA)) + Alltrim((clAlias)->PHU_AMBIEN)  } ) ) == 0
	
					aadd(aThreds, {	Alltrim((clAlias)->PHU_SERVID),;
									(clAlias)->PHU_PORTA 			,;
									Alltrim((clAlias)->PHU_AMBIEN),;
									{ {Alltrim((clAlias)->PHU_VARGBL) , (clAlias)->RECNOPHU}  } } )
				Else
				
					aadd(aThreds[nPs][4], {Alltrim((clAlias)->PHU_VARGBL) , (clAlias)->RECNOPHU  } )
					
				EndIf
				
				(clAlias)->(dbSkip())
			EndDo
		EndIf
	
		(clAlias)->(dbCloseArea())
		
		U_GVC7CLEAR(aThreds) 
		
		If lJob 
			RpcClearEnv()
		EndIf	
	
	EndIf
	
Return
