#INCLUDE "PROTHEUS.CH"

/*
TGCVJSRV(cEmp, cFil, cChave, nqThread, cRotJob, cRotThr, cRotErr, cRotEnd, nWaitTime)       // Gerenciador da threads
TGCVJDIS(cChave, cParaProc, nLimQtd)                           // Distribui para thread disponivel
TGCVJTHR(cEmp, cFil, cChave, cRotThr)                          // Thread distribuida
TGCVJEXIT(cChave)                                              // analisa solicitação de saia pelo monitor
TGCVJMON(cTitulo, cChave, nqThread, cRotJob, cRotThr, cRotPar, cRotErr, cRotEnd) // monitor das threads

JGetPar(cChave, nqThread, cRotJob, cRotThr, cRotPar, cRotErr, cRotEnd) // monitor das threads
JFim(cChave)                                                  // aguarda o termino de todas as threads
JWait(cChave)                                                 // aguarda a ociosidade de todas as threads  
JSaveQry(cChave, cQuery)                                      // Salva a query
JSaveArq(cArquivo, cLinha, lCRLF)                             // Grava Arquivo de log
JSavePar(cChave, aPar)                                        // salva os parametos a serem utilizados no job
JLoadPar(cChave)                                              // retorna array com parametros
JMsg(cChaveSrv)                                               // Retorna a ultima msg do job
JOnLine(cChave)
JManuIp() ou TGCVJMIP                                         // Rotina de manutenção da maquinas do job
JGetInfo()                                                    // Retorna informações de variaveis do processamento    
JSetProc()                                                    // Seta a quantidade de processo finalizados                                                 
*/


Static __aSem     := {}
Static __cDir     := ""
Static __nVezDis  := -1
Static __nQtdGo   := 0       // Quantidade de processos distribuidos
Static __nLimite  := 0       // limite de threads disponiveis apuradas no conjunto de maquinas
Static __nQtdProc := 0       // Quantidade de processos finalizados
Static __nTCapa   := 0       // Total de capacidade de threads
Static __nQtdThr  := 0       // Quantidade de threads iniciadas
Static __nWaitTime:= 0

Static __cErroSrv:= ""
Static __cErroThr:= ""
Static __cEnvSrv := ""
Static __cIPSrv  := ""
Static __cPorSrv := "0"
Static __oSrv    := NIL
Static __aIPExec := {}
Static __cMyIp   := NIL
Static __cMyPort := NIL
Static __cErroP  := ""
Static __cRotThr := ""
Static __cRotErr := ""
Static __nIPVez  := 1
Static __aoSrv   := {}
Static __cIDGra  := ""

User Function TGCVJSRV(cEmp, cFil, cChave, nqThread, cRotJob, cRotThr, cRotErr, cRotEnd, nWaitTime)
    Local cChaveSrv := "SRV_" + cChave 
    Local cArqLog   := cChaveSrv + ".log"
    Local cArqSema  := cChaveSrv + ".sem"
    Local cArqFim   := cChaveSrv + ".fim"
    Local nSec      := Seconds()
    Local nSec2     := 0
    Local nDifSeg   := 0
    Local bErroA
    Local aRetPar   := {}
    

    Default nWaitTime := 5 * 60 // tempo de espera em segundos no default, 5 minutos = 300 segundos 

    __nWaitTime := nWaitTime

    If IsLegado(cChave) .or. Empty(nqThread)
        nqThread    := 0 
        __nWaitTime := 0
    EndIf     

    __cRotThr := cRotThr
    __cRotErr := cRotErr
    __cErroSrv := ""
    JCheckDir(cChave)
        
    Ferase(__cDir + cArqFim)

    If ! FechaSem(cArqSema)
        Return 
    EndIf
    __cMyIp   := NIL
    __cMyPort := NIL
    InfoConnect()
    CriaCfg(cChave)
    LoadCfg(cChave)

    If __nWaitTime == 0
        LimpaLog(.T.)
    EndIf 

    If Type("cEmpAnt") == "U"
        cEmpAnt := ""
    EndIf

    If Type("cFilAnt") == "U"
        cFilAnt := ""
    EndIf

    If cEmpAnt + cFilAnt != cEmp + cFil
        RpcSetEnv(cEmp, cFil )
    EndIf 

    set epoch to 1980
    set century on

    FWMonitorMsg( cChaveSrv)
    If __nWaitTime == 0
        ClearGlbSrv(cChaveSrv)
        ClearGlbSrv("JNTCAPA"  + cChaveSrv)
        ClearGlbSrv("JNLIMITE" + cChaveSrv)
        ClearGlbSrv("JNQTDTHR" + cChaveSrv)
        ClearGlbSrv("JNQTDPRO" + cChaveSrv)
        ClearGlbSrv("JNQTDERR" + cChaveSrv)
    Else
        IniGlbSrv(cChave)
    EndIf 
    
    aRetPar := LeParam(cChave) 

    GrvArq(cChaveSrv + ".log", VarInfo("Param", aRetPar,, .F. ))

    GrvArq(cArqLog, "Iniciado Job gerenciador de threads em (" + __cMyIp +":" +__cMyPort +") " + dtoc(Date()) + " " + Time())
    GrvArq(cArqLog, "")

    U_JGraIni(cChave)

        If nqThread > 0  .and. __nWaitTime > 0 
            nqThread -= RetQtdThread(cChave)
            If nqThread > 0
                GrvArq(cArqLog, "Iniciando " + Alltrim(Str(nqThread)) + " threads antes da distribuição " + Time())
                IniThreads(cChave, nqThread, cRotThr, cRotErr, __nWaitTime)
            EndIf 
        EndIf 

        nSec      := Seconds()
        bErroA   := ErrorBlock( { |oErro| ChkErrSrv( oErro ) } ) 

        Begin Sequence

            &cRotJob.(cChave, aRetPar ) //U_GCVA102G 

            aSize(aRetPar, 0)
            aRetPar := Nil

            IsEndThreads(cChave)

        End Sequence
        ErrorBlock( bErroA ) 

        If ! Empty(__cErroSrv) 
            GrvArq(cArqLog, __cErroSrv)
            __cErroSrv := ""
        EndIf

        nSec2 := Seconds()
        If nSec2 < nSec  // caso seja maior, significa que passaou da meia noite onde o seconds recomeça com 0
            nDifSeg := 86399 - nSec
            nDifSeg += nSec2
        Else
            nDifSeg := nSec2 - nSec            
        EndIf
        DescExec() // desconecta os executores

    GrvArq(cArqLog, "")
    If cRotEnd <> NIl 
        &cRotEnd.(cChave)
    EndIf 
    
    U_JGraEnd(cChave)

    GrvArq(cArqLog, "Finalizado Job gerenciador de threads " + Time())
    GrvArq(cArqLog, "Distribuido " + Alltrim(Transform(__nQtdGo, "@e 9,999,999"))+ " em :" + Timer(nDifSeg))
    GrvArq(cArqLog, "")
    AbreSem(cArqSema)

    ClearStatic()
    If cEmpAnt + cFilAnt != cEmp + cFil
        RpcClearEnv()
    EndIf 

Return

Static Function IsLegado(cChave)  // rotinas que utilizar multithrea antes do interceptor

    cChave := Upper(cChave)
    If  Left(cChave, 10) == "GERACROFIN"    .or. ;
        Left(cChave, 11) == "RESUMOFINAN"   .or. ;
        Left(cChave, 10) == "GERAPEDVEN"    .or. ; 
        Left(cChave, 07) == "GERANFS"       .or. ; 
        Left(cChave, 11) == "ATUALIZAZQE"   .or. ;
        Left(cChave, 13) == "NOVOCICLOFOTO" .or. ;
        Left(cChave, 12) == "INCORPORADOR"  .or. ;
        Left(cChave, 12) == "ATUACLIENTES"  .or. ;
        Left(cChave, 14) == "FECHAMENTOFOTO" 
        
        Return .t. 

    EndIf 

Return .f.

Static Function RetQtdThread(cChave)
    Local cChaveSrv := "SRV_" + cChave
    Local nQtde  := 0
    Local aInfo  := {}
    Local aLista := {}
    Local nx     := 0
    Local ny     := 0
    Local nThread   := 0

    aInfo  := AllInfoArray() 
    aLista := RetGlbSrv(cChaveSrv)

    For nx:= 1 to len(aLista)
        nThread := Val(aLista[nx, 8])
        For nY := 1 to len(aInfo) 
            If nThread == aInfo[ny, 3] 
                nQtde++
            EndIf
        Next
    Next

    aSize(aLista, 0)
    aLista := Nil
    aSize(aInfo , 0)
    aInfo  := Nil

Return nQtde

Static Function ClearStatic()
    Local ni  := 0

    For ni:=1 to Len(__aoSrv)
        FreeObj(__aoSrv[ni, 2])
        __aoSrv[ni, 2] := Nil
    Next

    aSize(__aSem   , 0)
    aSize(__aIPExec, 0)
    aSize(__aoSrv  , 0)

    __aSem    := Nil  
    __aIPExec := Nil
    __aoSrv   := Nil

    __aSem    := {}
    __aIPExec := {}
    __aoSrv   := {}
     
    FreeObj(__oSrv)
    __oSrv   := NIL

Return

Static Function JCheckDir(cChave)

    __cDir := "system\tijob\" 
    If ! ExistDir(__cDir)
        MakeDir(__cDir)
    Endif

    If cChave <> NIl
        __cDir := "system\tijob\" + cChave + "\"    
        If ! ExistDir(__cDir)
            MakeDir(__cDir)
        Endif
    EndIf
Return

Static Function Timer(nSec)
    Local nHour := 0
    Local nMin  := 0

    nSec := If(nSec == NIL, 0, nSec)

    nMin  := Int(nSec/60)
    nHour := Int(nMin/60)

    If ( nMin > 0 )
        nSec -= nMin*60
    EndIf

    If ( nHour > 0 )
        nMin -= nHour*60
    EndIf

Return StrZero(nHour,2,0)+":"+StrZero(nMin,2,0)+":"+StrZero(nSec,2,0)

User Function JFim(cChave)
    
Return IsEndThreads(cChave)

Static Function IsEndThreads(cChave)
    Local cChaveSrv := "SRV_" + cChave
    Local aLista    := {}
    Local nX        := 0
    Local nY        := 0
    Local aInfo     := {}
    Local cStatus   := ""
    Local cMsg      := ""
    Local nThread   := 0
    Local lOnline   := .t.
    Local cThread   := ""

    If __nWaitTime > 0  // se tiver nWaitTime, não aguarda o termino das threds
        Return .t. 
    EndIf 
    
    // avisar as threas que finalizou, criando o arquivo de termino
    MemoWrit(__cDir + "SRV_" + cChave + ".fim", "Fim")
        
    FWMonitorMsg( cChaveSrv + " Aguardando termino das threads")
    While lOnline
        lOnline  := .F.
        aSize(aInfo , 0)
        aSize(aLista, 0)

        aInfo    := AllInfoArray() //GetUserInfoArray()    
        aLista   := RetGlbSrv(cChaveSrv)
        
        For nx:= 1 to len(aLista)
            nThread := Val( aLista[nx, 8])
            cThread := aLista[nx, 1] 
            cStatus := RetGlbVar("STA" + cThread)

            For nY := 1 to len(aInfo) 
                If nThread == aInfo[ny, 3] 
                    Exit
                EndIf
            Next
            If nY > len(aInfo) 
                cStatus := ""
                cMsg := ""
            EndIf

            If cStatus $ "E;I" //E=Em execução I=Inicializando
                lOnline := .T.
                Exit
            EndIf
        Next
        Sleep(1000) 
    End

    aSize(aLista, 0)
    aLista := Nil

    aSize(aInfo , 0)
    aInfo  := Nil

Return

User Function JWait(cChave)


Return IsWaitThreads(cChave)

Static Function IsWaitThreads(cChave)
    Local cChaveSrv := "SRV_" + cChave
    Local aLista    := {}
    Local nX        := 0
    Local lAllWait  := .F.
    Local cStatus   := ""
    Local cThread   := ""
        
    // avisar as threas que finalizou, criando o arquivo de termino
    FWMonitorMsg( cChaveSrv + " Aguardando ociosidade de todas as threads")
    While ! lAllWait
        lAllWait := .T.
        aSize(aLista, 0)
        aLista   := RetGlbSrv(cChaveSrv)

        For nx:= 1 to len(aLista)
            cThread := aLista[nx, 1] 
            cStatus := RetGlbVar("STA" + cThread)

            If cStatus $ "E;I" //E=Em execução I=Inicializando
                lAllWait := .F.
                Exit
            EndIf
        Next
        Sleep(1000) 
    End

    aSize(aLista, 0)
    aLista := Nil

Return lAllWait


Static Function IniGlbSrv(cChave)
    Local cChaveSrv := "SRV_" + cChave 
    Local aJobThr   := {}
    Local aInfo     := AllInfoArray()
    Local nx        := 0
    Local cObs      := ""
    Local cChaveRef := "THR_" + cChave
    Local nLenChave := len(cChaveRef) 
    Local cChaveTHR := ""
    Local np        := 0
    Local nPIPExec  := 0
    Local cIP       := ""
    Local cPorta    := ""
    Local nThread   := 0
    Local aJobNew   := {}
    
    While ! LockByName(cChaveSrv, .F.,.F., .T. )
    	Sleep(50)    
	End

    aJobThr   := RetGlbSrv(cChaveSrv)

    // completa o aJobThr com base nas threads do executores
    For nx := 1 to len(aInfo)
        cObs	  := aInfo[nx, 11] 

        If ! cChaveRef == Left(cObs, nLenChave)
            Loop 
        EndIF 

        cChaveTHR   := Left(cObs, nLenChave + 3)

        np := Ascan(aJobThr, {|x| X[1] ==cChaveTHR })
        If np > 0 
            Loop 
        EndIf 

        nThread := aInfo[nx, 3]
        nPIPExec:= aInfo[nx, len(aInfo[nx])]     

        cIP     := __aIPExec[nPIPExec, 2]
        cPorta  := __aIPExec[nPIPExec, 3]

        Aadd(aJobThr, {cChaveTHR, Dtoc(Date()), Time(), "", "", cIP, cPorta, Str(nThread, 6)})
    Next
    // Elimina a threads desconectadas
    aJobNew := {}
    For nx:= 1 to len(aJobThr)
        nThread := Val(aJobThr[nx, 8])

        np := Ascan(aInfo, {|x| X[3] == nThread })
        If np > 0
            aadd(aJobNew, aclone(aJobThr[nx]))  
        EndIf 
    Next
    
    AtuGlbSrv(cChaveSrv, aJobNew)
    UnLockByName(cChaveSrv,.F.,.F., .T. )
Return  

Static Function AtuGlbSrv(cChaveSrv, aJobThr)
    Local cJson := ""
    Local nTimeOut  := 10
    Local oServer 
    Local cEnvServer := __cEnvSrv   //GetEnvserver()
    Local cIP        := __cIPSrv  //"*************"
    Local cPorta     := __cPorSrv //"7032"   
   
    cJson := FwJsonSerialize(aJobThr)

    If __cMyIp == Alltrim(cIP) .and. __cMyPort == cPorta 
        PutGlbValue(cChaveSrv, cJson)
    Else
        If  __oSrv == NIL
            oServer := TRpc():New(cEnvServer)
            If ! oServer:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut )	
                Return 
            EndIf
            __oSrv := oServer
        EndIf
        __oSrv:CallProc("PutGlbValue", cChaveSrv, cJson)
    EndIf
    
Return 



Static Function RetGlbSrv(cChaveSrv)
    Local cJson := ""
    Local nTimeOut  := 10
    Local oServer 
    Local cEnvServer := __cEnvSrv // GetEnvserver()
    Local cIP        := __cIPSrv  
    Local cPorta     := __cPorSrv 
    Local aJobThr    := {}

    If __cMyIp == Alltrim(cIP) .and. __cMyPort == cPorta
        cJson := GetGlbValue(cChaveSrv)
    Else
        If  __oSrv == NIL
            oServer := TRpc():New(cEnvServer)
            If ! oServer:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut )	
                Return {}
            EndIf
            __oSrv := oServer
        EndIf
        cJson := __oSrv:CallProc("GetGlbValue", cChaveSrv)
    EndIf
    FWJsonDeserialize(cJson, @aJobThr)

    FreeObj(oServer)
    oServer := Nil
    
Return aClone(aJobThr)

Static Function ClearGlbSrv(cChaveSrv)
    Local nTimeOut  := 10
    Local oServer 
    Local cEnvServer := __cEnvSrv  //GetEnvserver()
    Local cIP        := __cIPSrv  //"*************"
    Local cPorta     := __cPorSrv //"7032"   

    If __cMyIp == Alltrim(cIP) .and. __cMyPort == cPorta
        ClearGlbValue(cChaveSrv)  
    Else
        If  __oSrv == NIL    
            oServer := TRpc():New(cEnvServer)
            If ! oServer:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut )	
                Return 
            EndIf
            __oSrv := oServer
        EndIf 
        __oSrv:CallProc("ClearGlbValue", cChaveSrv)
    EndIf
Return 

/*
  =============================
*/
Static Function AtuGlbVar(cChave, cConteudo)
    Local nTimeOut  := 10
    Local oServer 
    Local cEnvServer := __cEnvSrv   //GetEnvserver()
    Local cIP        := __cIPSrv  //"*************"
    Local cPorta     := __cPorSrv //"7032"   
   
    If __cMyIp == Alltrim(cIP) .and. __cMyPort == cPorta 
        PutGlbValue(cChave, cConteudo)
    Else
        If  __oSrv == NIL
            oServer := TRpc():New(cEnvServer)
            If ! oServer:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut )	
                Return 
            EndIf
            __oSrv := oServer
        EndIf
        __oSrv:CallProc("PutGlbValue", cChave, cConteudo)
    EndIf
    
Return 



Static Function RetGlbVar(cChave)
    Local cConteudo  := ""
    Local nTimeOut   := 10
    Local oServer 
    Local cEnvServer := __cEnvSrv // GetEnvserver()
    Local cIP        := __cIPSrv  
    Local cPorta     := __cPorSrv 
    
    If __cMyIp == Alltrim(cIP) .and. __cMyPort == cPorta
        cConteudo := GetGlbValue(cChave)
    Else
        If  __oSrv == NIL
            oServer := TRpc():New(cEnvServer)
            If ! oServer:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut )	
                Return ""
            EndIf
            __oSrv := oServer
        EndIf
        cConteudo := __oSrv:CallProc("GetGlbValue", cChave)
    EndIf

    FreeObj(oServer)
    oServer := Nil
    
Return cConteudo


/*
  =============================
*/
Static Function InfoArray(cEnv, cIP, cPorta)
    Local nTimeOut  := 10
    Local aInfoThr  := {}
    Local bErroA
    Local nj 

    Default cEnv    := __cEnvSrv
    Default cIP     := __cIPSrv
    Default cPorta  := __cPorSrv

    If Empty(cIP)
        Return {}
    EndIf
    
    nj     := ascan(__aoSrv, { |x| x[1] == cIp + cPorta })
    If Empty(nj)
        aadd(__aoSrv, {cIp + cPorta, NIl, .F.} )
        nj:= len(__aoSrv)
        __aoSrv[nj, 2]:= TRpc():New(cEnv)
        __aoSrv[nj, 3]:= __aoSrv[nj, 2]:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut )
    EndIf
    If ! __aoSrv[nj, 3]
        Return {}
    EndIf

    __cErroP := ""
    bErroA   := ErrorBlock( { |oErro| ChkErrP( oErro ) } ) 
    Begin Sequence
        aInfoThr := __aoSrv[nj, 2]:CallProc("GetUserInfoArray")
    End Sequence
	ErrorBlock( bErroA ) 
    If ! Empty(__cErroP)
        __cErroP := ""
        __aoSrv[nj, 3] := .F.
        Return {}
    EndIf
      
    //aInfoThr := GetUserInfoArray()
    
    
Return aClone(aInfoThr)


Static Function ChkErrP(oErroArq)

    If oErroArq:GenCode > 0
        __cErroP := '(' + Alltrim( Str( oErroArq:GenCode ) ) + ') : ' + AllTrim( oErroArq:Description ) + CRLF
    EndIf 

    Break
Return 


Static Function AllInfoArray()
    Local aInfoThr  := {}
    Local nx := 0
    Local ny := 0
    Local nz := 0
    Local nTCapa := 0
    Local nTUso  := 0
    Local nTDisp := 0

    Local nCapMaq := 0
    Local nUsoMaq := 0
    Local aInfoIni  := {}
    Local aInfo     := {}
    Local cEnvServer:= ""
    Local cIp       := ""
    Local cPorta    := ""
    For nx:= 1 to len(__aIPExec)
        cEnvServer:= __aIPExec[nx, 1]
        cIp       := __aIPExec[nx, 2]
        cPorta    := __aIPExec[nx, 3]

        aInfoIni := Aclone(InfoArray(cEnvServer, cIp,cPorta))
        aInfo   := {}
        For nz := 1 to len(aInfoIni)
            If Empty(aInfoIni[nz, 11])
                Loop 
            EndIf 
            aadd(aInfoIni[nz], nx) // guarda a posicao __aIPExec
            aadd(aInfo, aClone(aInfoIni[nz]))
        Next 

        If __aIPExec[nx, 7] == "EXECUTOR" .and.  __aIPExec[nx, 8] == "Ok!"
            nCapMaq := __aIPExec[nx, 4]
            nUsoMaq := len(aInfo)  
            If nUsoMaq > nCapMaq
                nUsoMaq := nCapMaq
            EndIf 

            nTCapa += nCapMaq
            nTUso  += nUsoMaq
            nTDisp := nTCapa - nTUso 
        EndIf
    
        For ny:= 1 to len(aInfo)
            aadd(aInfoThr, aclone(aInfo[ny]))
        Next
    Next
    __nTCapa  := nTCapa
    __nLimite := nTDisp



    aSize(aInfoIni, 0)
    aSize(aInfo   , 0)
    

    aInfoIni := nil
    aInfo    := nil
    
Return aClone(aInfoThr)

User Function JSrvGetInfo(cChaveSrv)
    Local oServer  := ""
    Local cEnvSrv  := ""
    Local cIPSrv   := ""
    Local cPortSrv := ""
    Local nTimeOut := 10
    Local aInfo    := {0,0,0,0,0,0}
    Local cChave   := Subs(cChaveSrv, 5)

    If __cEnvSrv == NIL .OR. Empty(__cEnvSrv)
        InfoConnect()
        CriaCfg(cChave)
        LoadCfg(cChave)
    EndIf 

    cEnvSrv  := __cEnvSrv
    cIPSrv   := __cIPSrv 
    cPortSrv := __cPorSrv

    oServer := TRpc():New(cEnvSrv)
    If ! oServer:Connect(Alltrim(cIPSrv) , Val(cPortSrv) , nTimeOut )	
        Return aInfo
    EndIf

    __cErroP := ""
    bErroA   := ErrorBlock( { |oErro| ChkErrP( oErro ) } ) 
    Begin Sequence
        aInfo := oServer:CallProc("U_JGetInfo", cChaveSrv)
    End Sequence
    ErrorBlock(bErroA) 
    oServer:Disconnect()

    If ! Empty(__cErroP)
        Return aInfo
    EndIf

Return aInfo

User Function JGetInfo(cChaveSrv)

    Local nTCapa   := Val(GetGlbValue("JNTCAPA"   + cChaveSrv))
    Local nLimite  := Val(GetGlbValue("JNLIMITE"  + cChaveSrv))
    Local nQtdThr  := Val(GetGlbValue("JNQTDTHR"  + cChaveSrv))
    Local nQtdGo   := Val(GetGlbValue("JNQTDGO"   + cChaveSrv))
    Local nQtdProc := Val(GetGlbValue("JNQTDPRO"  + cChaveSrv))
    Local nQtdErro := Val(GetGlbValue("JNQTDERR"  + cChaveSrv))

Return {nTCapa, nLimite, nQtdThr, nQtdGo, nQtdProc, nQtdErro}


Static Function SetProcSrv(cChaveSrv, lErro)
    Local oServer  := ""
    Local cEnvSrv  := ""
    Local cIPSrv   := ""
    Local cPortSrv := ""
    Local nTimeOut := 10
    
    Local cChave   := Subs(cChaveSrv, 5)
    Local cFilecfg := "system\tijob\" + cChave + ".cfg"
    Local cParam   := ""
    Local aIPs     := {}
    Local nx       := 0

    Local cEnv     := GetEnvserver()
    Local cIP      := PegaIP()  
    Local cPorta   := AllTrim(GetPvProfString("TCP", "Port", "0", GetAdv97()))

    Default lErro := .F.

    If FindFunction("U_XJOBDEV")  //Para teste local em ambiente de desenvolvimento. Basta criar compilar o PE.
        aadd(aIPs, {cEnv, cIP, cPorta, 3 , 0, 0, "GERENCIADOR", "Ok!"})
        aadd(aIPs, {cEnv, cIP, cPorta, 7, 0, 0,  "EXECUTOR"  , "Ok!"})
    Else
        cParam := Alltrim(MemoRead(cFilecfg))
        FWJsonDeserialize(cParam, @aIPs)
    EndIf


    For nx:= 1 to Len(aIPs)
        If aIPs[nx, 7] == "GERENCIADOR"
            cEnvSrv  := aIPs[nx, 1]
            cIPSrv   := aIPs[nx, 2]
            cPortSrv := aIPs[nx, 3]
            Exit
        EndIf
    Next
    
    oServer := TRpc():New(cEnvSrv)
    If ! oServer:Connect(Alltrim(cIPSrv) , Val(cPortSrv) , nTimeOut )	
        Return .F.
    EndIf

    __cErroP := ""
    bErroA   := ErrorBlock( { |oErro| ChkErrP( oErro ) } ) 
    Begin Sequence
        oServer:CallProc("U_JSetProc", cChaveSrv, lErro)
    End Sequence
    ErrorBlock(bErroA) 
    oServer:Disconnect()

    If ! Empty(__cErroP)
        Return .F.
    EndIf

Return .t.

User Function JSetProc(cChaveSrv, lErro)
    Local cChaveBlq := "JSETPROC" + cChaveSrv
    Local nQtdProc  := 0
    Local nQtdErro  := 0

    //cChaveSrv := "SRV_GERACROFIN00"
    While ! LockByName(cChaveBlq, .F.,.F., .T.)
        Sleep(50)    
    End
    If ! lErro
        nQtdProc := Val(GetGlbValue("JNQTDPRO" + cChaveSrv))
        nQtdProc++
        PutGlbValue("JNQTDPRO" + cChaveSrv, Alltrim(Str(nQtdProc )))
    Else
        nQtdErro := Val(GetGlbValue("JNQTDERR" + cChaveSrv))
        nQtdErro++
        PutGlbValue("JNQTDERR" + cChaveSrv, Alltrim(Str(nQtdErro )))
    EndIf
    UnLockByName(cChaveBlq, .F., .F., .T.)

Return 
    

User Function TGCVJDIS(cChave, cParaProc, nLimQtd)
    Local cChaveTHR := ""
    Local nVezAtu   := __nVezDis
    Local cChaveSrv := "SRV_" + cChave 
    Local aJobThr   := RetGlbSrv(cChaveSrv)
    Local nQtdThr   := len(aJobThr) 
    
    AllInfoArray()
    __nQtdThr := nQtdThr
    PutGlbValue("JNTCAPA"  + cChaveSrv, Alltrim(Str(__nTCapa )))
    PutGlbValue("JNLIMITE" + cChaveSrv, Alltrim(Str(__nLimite)))
    PutGlbValue("JNQTDTHR" + cChaveSrv, Alltrim(Str(__nQtdThr)))
    
    If nLimQtd <> NIL .and. nQtdThr > nLimQtd
        nQtdThr := nLimQtd
    EndIf
    
    While .t.
        cChaveTHR:= "THR_" + cChave + StrZero(++__nVezDis, 3)
        If __nVezDis > nQtdThr
            __nVezDis := -1
        EndIf

        If SuperIPCGo( cChaveTHR, cParaProc, aJobThr)
            __nQtdGo++
            PutGlbValue("JNQTDGO" + cChaveSrv , Alltrim(Str(__nQtdGo )))
            Return .t.
        EndIf
        If nVezAtu == __nVezDis
            Exit
        EndIf
    End
    // todas as threads estao oculpadas
    If nQtdThr < __nLimite .and. __nWaitTime == 0
        IniThreads(cChave, 1, __cRotThr, __cRotErr, __nWaitTime)
    EndIf
    aSize(aJobThr, 0)
    aJobThr := Nil
    sleep(500)

Return .F.




Static Function SuperIPCGo(cChaveTHR, cParaProc, aJobThr)
    Local np  := 0
    Local nj  := 0     
    Local nTimeOut := 10
    Local cIP    := ""
    Local cPorta := ""
 
    np := Ascan(aJobThr, {|x| x[1] == cChaveTHR})
    If Empty(np)
        Return .f.
    EndIf

    cIp    := aJobThr[np, 6]
    cPorta := aJobThr[np, 7]
    nj     := ascan(__aoSrv, { |x| x[1] == cIp + cPorta })
    If Empty(nj)
        aadd(__aoSrv, {cIp + cPorta, NIl, .F.} )
        nj:= len(__aoSrv)

        __aoSrv[nj, 2]:= TRpc():New(GetEnvserver())
        __aoSrv[nj, 3]:= __aoSrv[nj, 2]:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut )
    EndIf
    If ! __aoSrv[nj, 3]
        Return .F.
    EndIf
    If __cIPSrv == cIp .and. __cPorSrv == cPorta
        lOk := IPCGo( cChaveTHR, cParaProc)
    Else
        lOk :=  __aoSrv[nj, 2]:CallProc("IPCGo", cChaveTHR, cParaProc)
    EndIf

Return lOk


Static Function DescExec()
    Local nx:= 0

    For nx:= 1 to len(__aoSrv)
        If __aoSrv[nx, 3]
            __aoSrv[nx, 2]:Disconnect()    
        EndIf
        FreeObj(__aoSrv[nx, 2])
    Next
    
    aSize(__aoSrv, 0)
    __aoSrv := Nil
    __aoSrv := {}
Return

   

User Function TGCVJTHR(cEmp, cFil, cChave, cRotThr, cRotErr, nWaitTime)
    Local cChaveSrv := "SRV_" + cChave 
    Local cPar      := ""
    Local cRetorno  := ""
    Local aJobThr   := {}
    Local cChaveTHR := ""
    Local cArqLog   := ""
    Local cArqFim   := ""
    Local bErroA
    Local lInicio   := .T.
    Local cPortaTcp := AllTrim(GetPvProfString("TCP","Port","0", GetAdv97()))
    Local nSecIni   := 0 
    Local nCount    := 0
    Local cMsg      := ""

    JCheckDir(cChave)                    

    InfoConnect()
    LoadCfg(cChave)  // atualiza static com informações do IP e porta do Server

    set epoch to 1980
    set century on
    set date british 

    While ! LockByName(cChaveSrv, .F.,.F., .T. )
    	Sleep(50)    
	End
    aJobThr   := RetGlbSrv(cChaveSrv)

    If nWaitTime == 0
        cChaveTHR := "THR_" + cChave + ProxSeq(aJobThr)
    Else 
        cChaveTHR := "THR_" + cChave + BuscaSeq(cChave, aJobThr)
    EndIf 


    cArqLog   := cChaveTHR + ".log"
    cArqFim   := cChaveTHR + ".fim"

    Aadd(aJobThr, {cChaveTHR, Dtoc(Date()), Time(), "", "", PegaIP(), cPortaTcp, Str(ThreadId(), 6)})
    AtuGlbSrv(cChaveSrv, aJobThr)
    UnLockByName(cChaveSrv,.F.,.F., .T. )


    GrvArq(cChaveSrv + ".log", "   Iniciando threads (" + __cMyIp +":" +__cMyPort +") " + cChaveTHR + " " + dtoc(Date()) + " " + Time())

    Ferase(__cDir + cArqLog)
    Ferase(__cDir + cArqFim)
    
    cMsg :=  cChaveTHR + " [" + Alltrim(Str(nCount)) + "] - Iniciando ambiente..." 
    FWMonitorMsg(cMsg)
    DescThread(cChaveTHR, cMsg, "I")

    RpcSetEnv(cEmp, cFil)
 
    set epoch to 1980
    set century on
    
    cMsg :=  cChaveTHR + " [" + Alltrim(Str(nCount)) + "] - Aguardando..." 
    FWMonitorMsg(cMsg)
    DescThread(cChaveTHR, cMsg, "I")
   
    GrvArq(cArqLog, "Inicio da Thread (" + __cMyIp +":" +__cMyPort +") " + cChaveTHR + " " + dtoc(Date()) + " " + Time())
    While .t.
        If killapp()  // solicitação de saida do monitor Protheus
            GrvArq(cArqLog, "   Saindo por KillApp " + Time())
            Exit
        EndIf

        If File(__cDir + cArqFim)   // solicitação de saida do Monitor de Jobs
            GrvArq(cChaveSrv + ".log", "   Solicitação de saida pelo Monitor de Job, thread " + cChaveTHR + " " + Time())
            exit
        Endif

        If File(__cDir + cChaveSrv + ".fim")  // termino do serviço do gerenciador
            exit
        EndIf
        
        If ! File(__cDir + cChaveSrv + ".sem")  .and. nWaitTime == 0 // termino do serviço do gerenciador
            exit
        EndIf

       
        If lInicio
            lInicio := .F.
            GrvArq(cChaveSrv + ".ctl" , "Criação da thread " + cChaveTHR)
        EndIf
        If IpcWaitEx(cChaveTHR, 500, @cPar)
            
            cMsg := cChaveTHR + " [" + Alltrim(Str(++nCount)) + "] "  + cPar
            FWMonitorMsg(cMsg)
            DescThread(cChaveTHR, cMsg, "E")

            bErroA   := ErrorBlock( { |oErro| ChkErrThr( oErro, cRotErr, cChave, cPar, cChaveTHR ) } ) 
            
            Begin Sequence

                cRetorno := &cRotThr.(cChaveTHR, __cDir, cPar)

            End Sequence
            //FWMonitorMsg( cChaveTHR)
            
            ErrorBlock( bErroA )            
            cMsg := cChaveTHR + " [" + Alltrim(Str(nCount)) + "] - Aguardando..."
            
            FWMonitorMsg(cMsg)
            DescThread(cChaveTHR, cMsg, "P")           

            If ! Empty(__cErroThr) 
                GrvArq(cArqLog, __cErroThr)
                GrvArq(cChaveSrv + ".log",  CRLF + "****** Erro na Thread: " + cChaveTHR + " " + Time() + " " + cPar + CRLF )
                SetProcSrv(cChaveSrv, .T.)
                __cErroThr := ""
                //Exit //TCQUIT() //__Quit()
            Else
                If ValType(cRetorno) <> "C"
                    cRetorno := cValtoChar(cRetorno)
                EndIf 
                If ! Empty(cRetorno)
                    GrvArq(cArqLog, cRetorno)
                EndIf 
                SetProcSrv(cChaveSrv)
            EndIf
            nSecIni := 0
        EndIf

        cMsg :=  cChaveTHR + " [" + Alltrim(Str(nCount)) + "] - Aguardando..." 
        FWMonitorMsg(cMsg)
        DescThread(cChaveTHR, cMsg, "A")

        If IsEndTimeOut(cChaveTHR, @nSecIni, nWaitTime)
            GrvArq(cArqLog           , " *** Finalizando por timeout ***" + Time())
            Exit 
        EndIf 
        
    End

    GrvArq(cChaveSrv + ".log", "   Termino da thread " + cChaveTHR + " " + Time())
    GrvArq(cArqLog           , "Termino Thread " + Time())

    aSize(aJobThr, 0)
    aJobThr := Nil
    RpcClearEnv()
Return

Static Function DescThread(cChaveTHR, cMsg, cStatus)

    AtuGlbVar("MSG" + cChaveTHR, cMsg)
    AtuGlbVar("STA" + cChaveTHR, cStatus)

Return 


Static Function IsEndTimeOut(cChaveTHR, nSecIni, nWaitTime)
    Local nDifSeg := 0
    Local nSec2   := 0

    If nWaitTime == 0 
        Return .F.
    EndIf 

    If nWaitTime > 7200  // maior que 2 horas fixa em 2 horas
        nWaitTime := 7200
    EndIf   

    If nSecIni == 0 
        nSecIni := Seconds()
    EndIf 

    nSec2 := Seconds()
    If nSec2 < nSecIni  // caso seja maior, significa que passaou da meia noite onde o seconds recomeça com 0
        nDifSeg := 86399 - nSecIni
        nDifSeg += nSec2
    Else
        nDifSeg := nSec2 - nSecIni            
    EndIf

    If nDifSeg >= nWaitTime
        Return .t. 
    EndIf 


Return .F.

Static Function ChkErrSrv(oErroArq)
    Local ni:= 0
    
    __cErroSrv := ""
    __cErroSrv += Repl("=", 80) + CRLF 
    __cErroSrv += "Erro rotina do gerenciador:" + CRLF 
    __cErroSrv += CRLF

    If oErroArq:GenCode > 0
        __cErroSrv += '(' + Alltrim( Str( oErroArq:GenCode ) ) + ') : ' + AllTrim( oErroArq:Description ) + CRLF
    EndIf 
    ni := 2
    While ( !Empty(ProcName(ni)) )
        __cErroSrv +=	Trim(ProcName(ni)) +"(" + Alltrim(Str(ProcLine(ni))) + ") " + CRLF
        ni++
    End   
    __cErroSrv += Repl("=", 80) + CRLF 
    
Return 

Static Function ChkErrThr(oErroArq, cRotErr, cChave, cPar, cChaveTHR)
    Local ni:= 0
    
    __cErroThr :=""
    __cErroThr += Repl("=", 80) + CRLF
    __cErroThr += "Erro rotina do executor: " + CRLF
    __cErroThr += "Thread: " + Str(ThreadId(), 6) + " " + Time() + CRLF 
    __cErroThr += "Parametros:" + cPar + CRLF 
    __cErroThr += "Chave Thread: " + cChaveTHR + CRLF
    __cErroThr += CRLF

    If oErroArq:GenCode > 0
        __cErroThr += '(' + Alltrim( Str( oErroArq:GenCode ) ) + ') : ' + AllTrim( oErroArq:Description ) + CRLF
    EndIf 
    ni := 2
    While ( !Empty(ProcName(ni)) )
        __cErroThr +=	Trim(ProcName(ni)) +"(" + Alltrim(Str(ProcLine(ni))) + ") " + CRLF
        ni++
    End   

    If cRotErr <> NIL .and. ! Empty(cRotErr)
        __cErroThr += "Antes de executar a rotina de tratamento de erro " + cRotErr  + CRLF
        __cErroThr := &cRotErr.(cChave, cPar, __cErroThr)
        __cErroThr += "Depois de executar a rotina de tratamento de erro " + cRotErr  + CRLF
    EndIf
    __cErroThr += Repl("=", 80) + CRLF
    
    
Return 

User Function TGCVJEXIT(cChave)
    Local cChaveSrv := "SRV_" + cChave  
    Local cArqFim   := cChaveSrv + ".fim"

    If killapp()  // solicitação de saida do monitor Protheus
        Return .T.
    EndIf

    If File(__cDir + cArqFim)  // solicitação de saida do Monitor de Jobs
        GrvArq(cChaveSrv + ".log", "   Solicitação de saida pelo Monitor de Job." + Time())
        Return .T.
    Endif 

Return .F.

User Function JSavePar(cChave, aListaPar) 

Return SalvaParam(cChave, aListaPar)

Static Function SalvaParam(cChave, aListaPar) 
    Local cParam := ""
    Local cChaveSrv := "SRV_" + cChave

    cParam := FwJsonSerialize(aListaPar) 
    MemoWrit(__cDir + cChaveSrv + ".par", cParam)

Return 

User Function JLoadPar(cChave)

Return LeParam(cChave) 

Static Function LeParam(cChave) 
    Local aListaPar := {}
    Local cParam    := ""
    Local cChaveSrv := "SRV_" + cChave 
 
    If ! File(__cDir + cChaveSrv + ".par")
        Return {}
    EndIf

    cParam := Alltrim(MemoRead(__cDir + cChaveSrv + ".par"))
    FWJsonDeserialize(cParam, @aListaPar)
    
    //GrvArq(cChaveSrv + ".log", VarInfo("Param", aListaPar,, .F. ))
    
    
Return aClone(aListaPar)


Static Function IniThreads(cChave, nqThread, cRotThr, cRotErr, nWaitTime)
    Local nx        := 1
    Local cChaveSrv := "SRV_" + cChave 
    Local aIps      := __aIPExec
    Local nLenIPs   := len(__aIPExec)
    Local cEnvServer:= ""
    Local cIP       := ""
    Local cPorta    := ""
    Local nTimeOut  := 10
    Local aoSrv     := {}
    Local nj        := 0

    While nx <= nqThread

        If File(__cDir + cChaveSrv + ".fim")  // termino do serviço do gerenciador
            exit
        EndIf

        cEnvServer:= aIps[__nIPVez, 1]
        cIp       := aIps[__nIPVez, 2]
        cPorta    := aIps[__nIPVez, 3]

        If __cMyIp == Alltrim(cIP) .and. __cMyPort == cPorta 
        
            StartJob("U_TGCVJTHR", cEnvServer, .F., cEmpAnt, cFilAnt, cChave, cRotThr, cRotErr, nWaitTime)
            Sleep(100)     
            nx++
        Else
        
            nj     := ascan(aoSrv, { |x| x[1] == cIp + cPorta })
            If Empty(nj)
                aadd(aoSrv, {cIp + cPorta, NIl, .F.} )
                nj:= len(aoSrv)
                aoSrv[nj, 2]:= TRpc():New(cEnvServer)
                aoSrv[nj, 3]:= aoSrv[nj, 2]:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut )
            EndIf
            
            If  aoSrv[nj, 3]
                aoSrv[nj, 2]:CallProc("StartJob", "U_TGCVJTHR", GetEnvServer(), .F., cEmpAnt, cFilAnt, cChave, cRotThr, cRotErr, nWaitTime)
                Sleep(100)
                nx++
            EndIf    
        EndIf
        
        While ! File(__cDir + cChaveSrv + ".ctl")
            Sleep(100)
           
            If File(__cDir + cChaveSrv + ".fim")  // termino do serviço do gerenciador
                exit
            EndIf
        End
        FErase(__cDir + cChaveSrv + ".ctl")
        
        __nIPVez++
        If __nIPVez > nLenIPs
            __nIPVez := 1
        EndIf        
    End

    For nj := 1 to len(aoSrv)
        If  aoSrv[nj, 3]
            aoSrv[nj, 2]:Disconnect()
        EndIf  
        FreeObj(aoSrv[nj, 2])  
    Next

    Asize(aoSrv, 0)
    aoSrv := nil
    
Return 


Static Function FechaSem(cArqSema)
    Local nHSem

    nHSem  := MSFCreate(__cDir + cArqSema) 
    If nHSem  < 0                  
        Return .F.
    EndIf
    FWrite(nHSem, "")

    aadd(__aSem, {__cDir + cArqSema, nHSem})

Return .T.

Static Function AbreSem(cArqSema)
    Local nHSem
    Local nP

    nP := Ascan(__aSem, {|x| x[1] == __cDir + cArqSema})
    If Empty(np)
        Return
    EndIf
    nHSem := __aSem[np, 2]
    Fclose(nHSem)
    FErase(__cDir + cArqSema)
Return

Static Function BuscaSeq(cChave, aJobThr)
    Local cSeq      := "000"
    Local cChaveTHR := "THR_" + cChave  
    Local np        := 0

    While cSeq < StrZero(len(aJobThr), 3)
        np := ascan(aJobThr, {|x| x[1] == cChaveTHR + cSeq })
        If Empty(np)
            Exit 
        EndIf 
        cSeq := Soma1(cSeq)
    End 

Return cSeq 

Static Function ProxSeq(aJobThr)
    Local cSeq := "000"
    Local nQt  := len(aJobThr)

    If ! Empty(aJobThr)
        cSeq := StrZero(nQt, 3)
    EndIf

Return cSeq

User Function JSaveArq(cArquivo, cLinha, lCRLF) 

Return GrvArq(cArquivo, cLinha, lCRLF)

Static Function GrvArq(cArquivo, cLinha, lCRLF)
    Local nHandle
    Local nc      := 0
    Default lCRLF := .T.
    
    While ! LockByName(cArquivo, .F.,.F., .T.)
        Sleep(50)    
    End
    While .t. 
        
        If ! File(__cDir + cArquivo)
            If (nHandle := MSFCreate(__cDir + cArquivo, 0)) == -1
                nc++
                If nc >  10
                    Exit 
                EndIf 
                sleep(100)
                loop
            EndIf
        Else
            If (nHandle := FOpen(__cDir + cArquivo, 2)) == -1
                nc++
                If nc >  10
                    Exit
                EndIf 
                sleep(100)
                loop 
            EndIf
        EndIf
        FSeek(nHandle, 0, 2)
        FWrite(nHandle, cLinha + If(lCRLF, CRLF, ""))
        FClose(nHandle)
        Exit 
    End 
    UnLockByName(cArquivo, .F., .F., .T.)

Return

User Function JOnLine(cChave)
    Local cChaveSrv := "SRV_" + cChave 
    Local cArqSema  := cChaveSrv + ".sem"
    
    JCheckDir(cChave)

    If File(__cDir + cArqSema) .and. FErase(__cDir + cArqSema ) < 0
        Return .T.
    EndIf

Return .F.

User Function JMsg(cChaveSrv, cMsg)
    Local cChave := Subs(cChaveSrv, 5)
    JCheckDir(cChave)
    If cMsg == NIL
        Return MemoRead(__cDir + cChaveSrv + ".msg") 
    else
        MemoWrite(__cDir + cChaveSrv + ".msg", cMsg)
    EndIf 
Return 

User Function TGCVJMON(cTitulo, cChave, nqThread, cRotJob, cRotThr, cRotPar, cRotErr, cRotEnd, lAtivaSrv, nWaitTime)
    Local oPanel1
    Local oDlg                 
    Local oLbx
    Local aCab	:= {"Controle", "Data", "Hora", "Status ", "Descrição da Execução", "IP", "Porta", "Thread"}
    Local lOk   := .F.    
    Local aLista := {}
    Local oTimer  

    Local cChaveSrv := "SRV_" + cChave 
    Local cArqLog   := cChaveSrv + ".log"
    Local cArqSema  := cChaveSrv + ".sem"
    Local aButtons  := {}
    Local oRect   
    Local oRodaPe

    Default lAtivaSrv := .T. 
    Default nWaitTime := 0

    
    If lAtivaSrv
        Aadd(aButtons,{"PMSCOLOR", {|| AtivarServ(cChave, nqThread, cRotJob, cRotThr, cRotPar, cRotErr, cRotEnd, .t., nWaitTime )}   , "Ativar Serviço" ,"Ativar o gerenciador de Threads"})
    EndIf
    Aadd(aButtons,{"PMSCOLOR", {|| DesativarServ(cChave, nqThread, cRotJob, cRotThr)}, "Desativar Serviço" ,"Desativar o gerenciador de Threads"})
    Aadd(aButtons,{"PMSCOLOR", {|| IncThread(cChave, cRotThr, cRotErr, nWaitTime)} , "Incluir Threads" ,"Incluir Threads"})
    Aadd(aButtons,{"PMSCOLOR", {|| DesativaThread(aLista[oLbx:nAt, 1])} , "Desativar Threads" ,"Desativar Thread posicionada"})
    Aadd(aButtons,{"PMSCOLOR", {|| MostraLog(cArqLog)} , "Log" ,"Log do gerenciador "})
    Aadd(aButtons,{"PMSCOLOR", {|| U_JManuIp(cChave)}, "IPs" ,"Configuração de IPs "})
    Aadd(aButtons,{"PMSCOLOR", {|| MostraQry()}, "Query" ,"Ultima Query"})
    Aadd(aButtons,{"PMSCOLOR", {|| CopiaLog()} , "Copia os logs" , "Copia os arquivos gerados no server"})
    Aadd(aButtons,{"PMSCOLOR", {|| LimpaLog()} , "Limpa Arq(s) Log, csv,..." ,"Excluir os arquivos de controles, logs, csv,... "})
        
    Private lOnline := .F.

    JCheckDir(cChave)

    __cMyIp   := NIL
    __cMyPort := NIL
    InfoConnect()
    CriaCfg(cChave)
    LoadCfg(cChave)

    If File(__cDir + cArqSema) .and. FErase(__cDir + cArqSema ) < 0
        lOnline := .T.
    Else
        lOnline := .F.
    EndIf

    If Empty(aLista)
        aadd(aLista, {Space(20), Space(20), Space(20), Space(40), Space(100), Space(20), Space(06), Space(06)})
    EndIf

    oRect := TRect():New(0, 0, oMainWnd:nBottom * 0.90, oMainWnd:nRight * 0.80)	

    DEFINE MSDIALOG oDlg TITLE "Monitor Threads - " + cTitulo  FROM 0, 0 TO 680, 1000 PIXEL OF oMainWnd
        
        oDlg:SetCoors(oRect)
    
       	EnchoiceBar( oDlg, {||  nSelect := oLbx:nAt, lOk:= .t., oDlg:End()} , {|| nSelect:=0,oDlg:End()},, aButtons)   

        oPanel1 :=TPanel():New( 010, 010, ,oDlg, , , , , , 70, 70, .F.,.T. )
        oPanel1 :align := CONTROL_ALIGN_ALLCLIENT
        oLbx:= TwBrowse():New(01,01,490,490,,aCab,, oPanel1,,,,,,,,,,,,.F.,,.T.,,.F.,,,)
        oLbx:bLDblClick 	:= { || Mostra(aLista, oLbx:nAt) }
        oLbx:align := CONTROL_ALIGN_ALLCLIENT     
        oLbx:SetArray( aLista )
        oLbx:bLine := {|| Retbline(oLbx,aLista) }	
        oLbx:Refresh()	

        oRodaPe:= TSimpleEditor():New( 0,0,oPanel1, 40, 40 )
        oRodaPe:Align := CONTROL_ALIGN_BOTTOM

        oDlg:lMaximized := .T.

        DEFINE TIMER oTimer INTERVAL 1000 ACTION AtuTela(oLbx, aLista, oTimer, oDlg, cArqSema, cTitulo, cChave, oRodaPe ) OF oDlg

    ACTIVATE MSDIALOG oDlg ON INIT (AtuTela(oLbx, aLista, oTimer, oDlg, cArqSema, cTitulo, cChave, oRodaPe), oTimer:Activate())  CENTERED
    //ACTIVATE MSDIALOG oDlg  CENTERED
    aSize(aLista, 0)
    aLista := Nil 
Return  lOk    



Static Function CopiaLog()
    Local cDirRmt := ""
    Local cDirSrv := __cDir
    Local aDir    := {}
    Local cArq    := ""
    Local nx      := 0

    cDirRmt := TFileDialog("All(*)",'Informe o diretorio destino para a copia dos arquivos gerados',0,"/tmp",.F.,GETF_RETDIRECTORY)
    If Empty(cDirRmt)
        Return 
    EndIf 

    If ! FWAlertYesNo("Confirma a copia dos arquivos para o diretorio " + cDirRmt)
        Return 
    EndIf 
    
    aDir := Directory(cDirSrv + "*.*")
    If Empty(aDir)
        FWAlertInfo("Não existe arquivo criados")
        Return 
    EndIf 
    For nX := 1 to len(aDir)
        cArq := aDir[nx, 1]
        CpyS2T(cDirSrv + cArq, cDirRmt)
    Next
    FWAlertInfo("Copia de arquivos concluida!")

Return 

User Function JGetPar(cChave, nqThread, cRotJob, cRotThr, cRotPar, cRotErr, cRotEnd, lConfirma, nWaitTime, lMesmoAmb)
    Local cChaveSrv := "SRV_" + cChave 
    Local cArqSema  := cChaveSrv + ".sem"
    Local lRet      := .F.
    Private lOnline := .F.

    Default lConfirma := .T.
    Default lMesmoAmb := .F.
    
    JCheckDir(cChave)

    If File(__cDir + cArqSema) .and. FErase(__cDir + cArqSema ) < 0
        If ! IsBlind()
            MsgInfo("Job em execução!!")
        EndIf 
        Return .T.
    EndIf
    __cMyIp   := NIL
    __cMyPort := NIL
    InfoConnect()
    CriaCfg(cChave)
    LoadCfg(cChave)

    lRet := AtivarServ(cChave, nqThread, cRotJob, cRotThr, cRotPar, cRotErr, cRotEnd, lConfirma, nWaitTime, lMesmoAmb)

Return lRet

Static Function AtivarServ(cChave, nqThread, cRotJob, cRotThr, cRotPar, cRotErr, cRotEnd, lConfirma, nWaitTime, lMesmoAmb)
    Local cEmp  := cEmpAnt
    Local cFil  := cFilAnt 
    Local aRet  := {}
    Local oServer 
    Local cEnvServer := GetEnvserver()
    Local cIP     := __cIPSrv  //"*************"
    Local cPorta  := __cPorSrv //"7032"   
    Local nTimeOut  := 10

    Default lConfirma := .T.
    Default lMesmoAmb := .F.
    Default nWaitTime := 0
    
    
    If lOnline  //FwIsInCallSatck não enxerga mais o U_GCT000JOB na pilha, trata-se de outra thread
        If ! IsBlind()
            MsgAlert('Serviço já está ativo!')
        EndIf 
        Return .t.
    EndIf
    
    If cRotPar <> NIL // se for nil, foi executado pelo monitor
       
        
        aRet := &cRotPar.(cChave) //invoca GCVA102P
        If Empty(aRet)
           aRet := Nil
           Return .F.
        EndIf
        SalvaParam(cChave, aRet) 
        aSize(aRet, 0)
        aRet := Nil
    EndIf

    If ! IsBlind() .and. lConfirma .And. ! MsgYesNo("Confirma a execução do Job?")
        Return .F.
    EndIf

    If lMesmoAmb
        U_TGCVJSRV(cEmp, cFil, cChave, nqThread, cRotJob, cRotThr, cRotErr, cRotEnd, nWaitTime)
    ElseIf __cMyIp == Alltrim(cIP) .and. __cMyPort == cPorta

       StartJob("U_TGCVJSRV", GetEnvServer(), .F., cEmp, cFil, cChave, nqThread, cRotJob, cRotThr, cRotErr, cRotEnd, nWaitTime)
    Else
        oServer := TRpc():New(cEnvServer)
        If ! oServer:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut )
            FreeObj(oServer)
            oServer := Nil	
            Return .F.
        EndIf
        
		__cErroP := ""
    	bErroA   := ErrorBlock( { |oErro| ChkErrP( oErro ) } ) 
    	Begin Sequence
        	oServer:CallProc("StartJob", "U_TGCVJSRV", GetEnvServer(), .F., cEmp, cFil, cChave, nqThread, cRotJob, cRotThr, cRotErr, cRotEnd, nWaitTime)
		End Sequence
		ErrorBlock(bErroA)	
        oServer:Disconnect()
        FreeObj(oServer)
        oServer := Nil	
    EndIf
    

Return .T.

Static Function DesativarServ(cChave, nqThread, cRotJob, cRotThr)
    
    /*
    If ! lOnline
        MsgAlert('Serviço não está ativo!')
        Return
    EndIf
    */

    If MsgYesNo("Confirma a desativação do serviço")
        MemoWrit(__cDir + "SRV_" + cChave + ".fim", "Fim")
    Endif

Return 

Static Function IncThread(cChave, cRotThr, cRotErr, nWaitTime)
    Local nqThread := 0
    
    /*
    If ! lOnline 
        MsgAlert('Serviço não está ativo!')
        Return
    EndIf
    */

    nqThread := PegaQtd()
    
    If ! Empty(nqThread)
        IniThreads( cChave, nqThread, cRotThr, cRotErr, nWaitTime)
    EndIf

Return 

Static Function DesativaThread(cChaveTHR)
    
    /*
    If ! lOnline
        MsgAlert('Serviço não está ativo!')
        Return
    EndIf
    */

    If MsgYesNo("Confirma a desativação da thread " + cChaveTHR )
       MemoWrit(__cDir + cChaveTHR + ".fim", "Fim")
    EndIf
Return 

Static Function PegaQtd(nqThread)
    Local aParamBox := {}
    Local nQtde     := 0
    Local cQtde     := "0   "
    Local aRet      := {}
    Local cTitulo   := "Informe a quantidade de Threads"

    If nqThread <> NIL
        cQtde := Padr(Alltrim(Str(nqThread)), 4)
    EndIf

    MV_PAR01:=""

    aAdd(aParamBox,{1,"Quantidade",	cQtde, "9999", "NaoVazio()",,, 3, .T.})
    If ParamBox(aParamBox, cTitulo , @aRet) 
       nQtde := Val(aRet[1])
    EndIf

Return nQtde


Static Function LimpaLog(lJob)
    Local nx   := 0
    Local cArq := ""
    Local aDir := {}
    Local aDirZip := {}
    Local cArqZip := __cDir + "bkp_" + FWTimeStamp(1) + ".zip" 
    
    
    Default lJob := .F.

    If ! lJob
        /*
        If lOnline 
               MsgAlert('Serviço está ativo!')
            Return
        EndIf
        */

        If! MsgYesNo("Confirma a exclusão dos arquivos de log" )
              Return
        Endif
        
    EndIf
    aDir := Directory(__cDir + "*.*")
    For nX := 1 to len(aDir)
        cArq := aDir[nx, 1]
        If lower(Right(cArq, 4)) $ ".zip;"
            Loop
        EndIf
        aadd(aDirZip, __cDir + cArq )
    Next
    FZip(cArqZip, aDirZip)

    For nX := 1 to len(aDir)
        cArq := aDir[nx, 1]

        If lower(Right(cArq, 4)) $ ".par;.zip;"
            Loop
        EndIf
        FErase(__cDir + cArq)    
    Next
    
    aDir := Directory(__cDir + "*.zip")
    For nX := 1 to len(aDir)
        cArq := aDir[nx, 1]
        
        If Subs(cArq, 5, 6) == Left(Dtos(date()), 6)
            Loop 
        EndIf
        FErase(__cDir + cArq)    
    Next

Return
                          
Static Function RetbLine(oLbx, aLista) 
    Local nx           
    Local aRet	:= {}
    For nX := 1 to len(aLista[oLbx:nAt])
        aadd(aRet,aLista[oLbx:nAt,nX])
    Next
Return aclone(aRet)    

Static Function Mostra(aLista, nSelect)
    Local cFile := RetFileName(aLista[nSelect, 1])
    Local cFileLog := cFile + ".log"
    
    MostraLog(cFileLog)
    
Return


Static Function MostraLog(cArqLog)
    Local oDlg
    Local cMemo
    Local oMemo
    
    Local oFont:= TFont():New("Consolas",, 20,, .F.,,,,, .F. )
    Local cFileLog := __cDir + cArqLog
    Local oModal 
    
    cMemo :=TIMemoRead(cFileLog)

    oModal  := FWDialogModal():New()       
	oModal:SetEscClose(.T.)
    oModal:setTitle(cFileLog)
	oModal:setSize(240, 400)
    oModal:createDialog()
    oModal:addCloseButton({|| oModal:DeActivate() }, "OK")
    oDlg:= oModal:getPanelMain()
        
        oMemo := tMultiget():new(,, bSETGET(cMemo), oDlg)
        oMemo:Align := CONTROL_ALIGN_ALLCLIENT
        oMemo:oFont:=oFont

        oModal:Activate()

    
Return 

Static Function TIMemoRead(cFile)
    Local cRet := ""
    Local cFun  := "MemoRead"

    cRet := &(cFun)(cFile,)  //Não pode tirar a virgula

Return cRet 



Static Function AtuTela(oLbx, aLista, oTimer, oDlg, cArqSema, cTitulo, cChave, oRodaPe)
    Local nx:= 0
    Local ny:= 0
    Local cStatus := ""
    Local cChaveSrv := "SRV_" + cChave 
    
    Local aInfo   := AllInfoArray() // GetUserInfoArray()
    Local aInfoSrv:= InfoArray()
    Local cObs    := ""
    Local cMsg    := ""
    Local cArqThr := ""
    Local nP
    Local nThread := 0
    Local cThread := ""
    Local aDir    := {}

    If oTimer == NIL
        Return
    EndIf 
    
    oTimer:Deactivate()   
    
    If File(__cDir + cArqSema) .and. FErase(__cDir + cArqSema ) < 0
        lOnline := .T.
    Else
        lOnline := .F.
    EndIf

    IniGlbSrv(cChave)
       
    
    aLista   := RetGlbSrv(cChaveSrv)
    
    aDir := Directory(__cDir + "*.log")
    
    For nX := 1 to len(aDir)
        cArqThr := RetFileName(aDir[nx, 1])
        If ! "THR_" + cChave $ cArqThr
            Loop 
        EndIf 

        np  := Ascan(aLista, {|x| x[1] == cArqThr })
        If Empty(np)
            aadd(aLista, {cArqThr, Dtoc(aDir[nx, 3]), aDir[nx, 4], "", "", "", "",""})
        EndIf
    
    Next

    If Empty(aLista)
        aadd(aLista, {Space(20), Space(20), Space(20), Space(40), Space(100), Space(20), Space(6), Space(6)})
    EndIf
    
    For nx:= 1 to len(aLista)
        cThread := aLista[nx, 1] 
        cStatus := RetGlbVar("STA" + cThread)
        cMsg    := RetGlbVar("MSG" + cThread)

        If cStatus == "I"
            aLista[nX, 4] := "Inicializando"
        ElseIf cStatus == "A"
            aLista[nX, 4] := "Aguardando"
        ElseIf cStatus == "E"
            aLista[nX, 4] := "Em execução"
        Else
            aLista[nX, 4] := "Processado"
        EndIf        
        aLista[nX, 5] := cMsg

        nThread := Val(aLista[nx, 8])
        For nY := 1 to len(aInfo) 
            If nThread == aInfo[ny, 3] 
                Exit
            EndIf
        Next
        If nY > len(aInfo) 
            cStatus := ""
            cMsg := ""
            aLista[nX, 4] := cStatus
            aLista[nX, 5] := cMsg
        EndIf
    Next
   
    oLbx:SetArray( aLista )
    oLbx:bLine := {|| Retbline(oLbx,aLista) }	
    oLbx:Refresh()

    For nY := 1 to len(aInfoSrv)  
        cObs	   := aInfoSrv[ny, 11]        
        If cChaveSrv $ cObs
           cMsgSrv := cObs
           Exit
        EndIf
    Next
    If nY > len(aInfoSrv) 
       cMsgSrv := ""
    EndIf

    
    oRodaPe:Load(MontaHtml(cChaveSrv))
    oRodaPe:Refresh()

    oDlg:cCaption := "Monitor Threads (" + __cIPSrv + ":" + __cPorSrv + ") - " + cTitulo + " - " + Time() +If(lOnline," - (On Line) - " + cMsgSrv," - (Off Line)")
  
    oTimer:Activate()

    aSize(aInfo   , 0)   
    aSize(aInfoSrv, 0) 
    aInfo    := Nil
    aInfoSrv := Nil

Return

Static Function MontaHtml(cChaveSrv)
    Local aInfo := U_JSrvGetInfo(cChaveSrv)

    Local nTCapa   := aInfo[1]
    Local nLimite  := aInfo[2]
    Local nCount   := aInfo[3]
    Local nQtdGo   := aInfo[4]
    Local nQtdProc := aInfo[5]
    Local nQtdErro := aInfo[6]

    aSize(aInfo, 0)
    aInfo := Nil

    cHtml := ""
    cHtml += "   <table width=100% border=1 cellspacing=0 cellpadding=2 bordercolor='666633'>" + CRLF
    cHtml += "      <tr> " + CRLF
    cHtml += "         <td width='150' align='RIGHT'><b>Capacidade de Threads</b></td>" + CRLF
    cHtml += "         <td width='150' align='RIGHT'><b>Disponibilidade de Threads</b></td>" + CRLF
    cHtml += "         <td width='150' align='RIGHT'><b>Threads iniciadas</b></td>" + CRLF
    cHtml += "         <td width='150' align='RIGHT'><b>Distribuidos</b></td>" + CRLF
    cHtml += "         <td width='150' align='RIGHT'><b>Processados</b></td>" + CRLF
    cHtml += "         <td width='150' align='RIGHT'><b>Erro</b></td>" + CRLF    
    cHtml += "      </tr> "+ CRLF
    cHtml += "      <tr> " + CRLF
    cHtml += "         <td width='150' align='RIGHT'> " + Transform(nTCapa  , "@e 99,999,999,999")  +"</td>" + CRLF
    cHtml += "         <td width='150' align='RIGHT'> " + Transform(nLimite , "@e 99,999,999,999")  +"</td>" + CRLF
    cHtml += "         <td width='150' align='RIGHT'> " + Transform(nCount  , "@e 99,999,999,999")  +"</td>" + CRLF
    cHtml += "         <td width='150' align='RIGHT'> " + Transform(nQtdGo  , "@e 99,999,999,999")  +"</td>" + CRLF
    cHtml += "         <td width='150' align='RIGHT'> " + Transform(nQtdProc, "@e 99,999,999,999")  +"</td>" + CRLF
    cHtml += "         <td width='150' align='RIGHT'> " + Transform(nQtdErro, "@e 99,999,999,999")  +"</td>" + CRLF
    cHtml += "      </tr> "+ CRLF
    cHtml += "   </table>" + CRLF
Return cHtml 

User Function TGCVJMIP()
Return U_JManuIp()

User Function JManuIp(cChave)
    Local aIPs     := {}
    Local cFilecfg := "system\tijob\" + cChave + ".cfg"
    Local cParam   := ""
    Local cEnv     := GetEnvserver()
    Local cIP      := PegaIP()  
    Local cPorta   := AllTrim(GetPvProfString("TCP", "Port", "0", GetAdv97()))

    InfoConnect()
    CriaCfg(cChave)
    LoadCfg(cChave)

    If FindFunction("U_XJOBDEV")  //Para teste local em ambiente de desenvolvimento. Basta criar compilar o PE.
        aadd(aIPs, {cEnv, cIP, cPorta, 3 , 0, 0, "GERENCIADOR", "Ok!"})
        aadd(aIPs, {cEnv, cIP, cPorta, 7, 0, 0,  "EXECUTOR"  , "Ok!"})
    Else
        cParam := Alltrim(MemoRead(cFilecfg))
        FWJsonDeserialize(cParam, @aIPs)
    EndIf
   
    If Empty(aIPs)
        Return
    EndIf
    
    If ! TelaIP(aIPs)
        Return
    EndIf

    cParam := FwJsonSerialize(aIPs)
    Memowrit(cFilecfg, cParam)
    
    __cIPSrv  := ""
    __cPorSrv := "0"
    __oSrv    := NIL
    __aIPExec := {}
    LoadCfg(cChave)


Return

Static Function TelaIP(aIPs)
    Local aCab := {"Ambiente", "IP", "Porta", "Limite de threads", "Qtde de threads em uso", "Disponibilidade de threads", "Tipo", "Teste Conexão"}
    Local oPanel1
    Local oDlg                 
    Local lOk		:= .F.    
    Local aButtons := {}
    Local nx := 0
    Local nUso  := 0
    Local oTimer
    Local oRect
    Local oKey
    

    Private lChangeIP := .F.

    oKey := SetKey(K_ALT_I ,{|| lChangeIP := ! lChangeIP}) 

    
    For nx:= 1 to len(aIPs)
        /*
        If TesteIP(aIPs[nx, 1], aIPs[nx, 2], aIPs[nx, 3], @nUso)
            aIPs[nx, 8] := "Ok!"
        Else
            aIPs[nx, 8] := "Não conecta!"
        EndIf
        */

        aIPs[nx, 8] := ""
        aIPs[nx, 5] := nUso
        aIPs[nx, 6] := Max(aIPs[nx, 4] - nUso, 0)
    Next
    
    aAdd(aButtons,{"PMSCOLOR"		,{|| IncIP(oLbx, aIPs) }          , "Incluir IP","Incluir IP"})
    aAdd(aButtons,{"PMSCOLOR"		,{|| AltIP(oLbx, aIPs) }          , "Alterar IP","Alterar IP"})
    aAdd(aButtons,{"PMSCOLOR"		,{|| ExcIP(oLbx, aIPs) }          , "Excluir IP","Excluir IP"})
    aAdd(aButtons,{"PMSCOLOR"		,{|| Monitor(aIPs, oLbx, oTimer) }, "Monitor Threads","Monitor Threads"})


    oRect := TRect():New(0, 0, oMainWnd:nBottom * 0.70 , oMainWnd:nRight * 0.80)	
        
    DEFINE MSDIALOG oDlg FROM 0,0 TO 0,0 TITLE "Configuração de IPs"  //STYLE nOR(WS_VISIBLE,WS_POPUP)
        oDlg:SetCoors(oRect)
    
        EnchoiceBar( oDlg, {|| lOk:= ValidLista(aIPs) , If(lOk, oDlg:End(), .f.)} , {|| oDlg:End() },, aButtons )   
        oPanel1 :=TPanel():New( 010, 010, ,oDlg, , , , , , 70, 70, .F.,.T. )
        oPanel1 :align := CONTROL_ALIGN_ALLCLIENT
        oLbx:= TwBrowse():New(01,01,490,490,,aCab,, oPanel1,,,,,,,,,,,,.F.,,.T.,,.F.,,,)
        oLbx:align := CONTROL_ALIGN_ALLCLIENT     
        oLbx:SetArray( aIPs )
        oLbx:bLine       := {|| Retbline(oLbx, aIPs) }
        oLbx:bLDblClick  := {|| AltIP(oLbx, aIPs) }	
        oLbx:Refresh()	

        DEFINE TIMER oTimer INTERVAL 3000 ACTION AtuTelIP(oLbx, aIPs, oTimer, oDlg) OF oDlg

    ACTIVATE MSDIALOG oDlg ON INIT (AtuTelIP(oLbx, aIPs, oTimer, oDlg), oTimer:Activate())  CENTERED  

    SetKey(K_ALT_I , oKey)

Return lOk


Static Function ValidLista(aIPs)

    If Ascan(aIPs, {|x| x[7] == "GERENCIADOR" }) == 0
        MsgAlert("Necessario ter um IP do tipo Gerenciador!!!")
        Return .F.
    EndIf
Return .t.


Static Function IncIP(oLbx, aIPs)
    Local aParamBox := {}
    Local lCanSave  := .F.
    Local cEnv      := Space(30)
    Local cIP       := Space(20)
    Local cPorta    := Space(6)
    Local nCapacit  := 0
    Local aTipo     := {"Gerenciador", "Executor"}
    Local cTipo     := "Executor"
    Local aRet      := {}
    Local cTitulo   := "IP"
    Local cTeste    := ""
    Local nUso      := 0

    aAdd(aParamBox, {1,"Ambiente"   , cEnv    , "@!" , "NaoVazio()",,, 80, .T.})
    aAdd(aParamBox, {1,"IP"         , cIP     , "@!" , "NaoVazio()",,, 50, .T.})
    aAdd(aParamBox, {1,"Porta"      , cPorta  , "@!" , "NaoVazio()",,, 20, .T.})
    aAdd(aParamBox, {1,"Capacidade" , nCapacit, "999", "NaoVazio()",,, 20, .T.})
    aAdd(aParamBox, {2,"Tipo"       , cTipo   , aTipo   , 50 ,"" ,.F.}) 

    If ! ParamBox(aParamBox, cTitulo , @aRet,,,,,,,, lCanSave) 
       Return
    EndIf

    cEnv     := Alltrim(aRet[1])
    cIP      := Alltrim(aRet[2])
    cPorta   := Alltrim(aRet[3])
    nCapacit := aRet[4]

    If Left(aRet[5], 1) == "G"
        If Ascan(aIPs, {|x| x[7] == "GERENCIADOR" }) > 0
            MsgAlert("Permitido somente um IP como Gerenciador")
            Return
        EndIf
        cTipo := "GERENCIADOR"
    Else
        cTipo := "EXECUTOR"
    EndIf
    

    If TesteIP(cEnv, cIP, cPorta, @nUso)
        cTeste := "Ok!"
    Else
        cTeste := "Não conecta!"
    EndIf

    aadd(aIPs , {cEnv, cIP, cPorta, nCapacit , nUso, Max(nCapacit - nUso, 0) , cTipo, cTeste})
    oLbx:SetArray( aIPs )
    oLbx:bLine := {|| Retbline(oLbx, aIPs) }
    oLbx:Refresh()	

Return

Static Function AltIP(oLbx, aIPs)
    Local aParamBox := {}
    Local lCanSave  := .F.
    Local cEnv      := Padr(aIPs[olbx:nAt, 1], 30)
    Local cIP       := aIPs[olbx:nAt, 2]
    Local cPorta    := Padr(aIPs[olbx:nAt, 3], 5)
    Local nCapacit  := aIPs[olbx:nAt, 4]
    Local aTipo     := {"Gerenciador", "Executor"}
    Local cTipo     := If(Left(aIPs[olbx:nAt, 7], 1) == "G", "Gerenciador", "Executor")
    Local aRet      := {}
    Local cTitulo   := "IP"
    Local cTeste    := ""
    Local nUso      := 0
    Local nP        := 0

    aAdd(aParamBox, {1,"Ambiente"   , cEnv    , "@!" , "NaoVazio()",,, 80, .T.})
    aAdd(aParamBox, {1,"IP"         , cIP     , "@!" , "NaoVazio()",,, 50, .T.})
    aAdd(aParamBox, {1,"Porta"      , cPorta  , "@!" , "NaoVazio()",,, 20, .T.})
    aAdd(aParamBox, {1,"Capacidade" , nCapacit, "999", "NaoVazio()",,, 20, .T.})
    aAdd(aParamBox, {2,"Tipo"       , cTipo   , aTipo   , 50 ,"" ,.F.}) 

    If ! ParamBox(aParamBox, cTitulo , @aRet,,,,,,,, lCanSave) 
       Return
    EndIf

    cEnv     := Alltrim(aRet[1])
    cIP      := Alltrim(aRet[2])
    cPorta   := Alltrim(aRet[3])
    nCapacit := aRet[4]

    If Left(aRet[5], 1) == "G"
        nP := Ascan(aIPs, {|x| x[7] == "GERENCIADOR" }) 

        If nP > 0 .and. nP <> olbx:nAt  
            MsgAlert("Permitido somente um IP como Gerenciador")
            Return
        EndIf
        cTipo := "GERENCIADOR"
    Else
        cTipo := "EXECUTOR"
    EndIf
    

    If TesteIP(cEnv, cIP, cPorta, @nUso)
        cTeste := "Ok!"
    Else
        cTeste := "Não conecta!"
    EndIf

    aIPs[olbx:nAt]  := {cEnv, cIP, cPorta, nCapacit , nUso, Max(nCapacit - nUso, 0) , cTipo, cTeste}
    oLbx:SetArray( aIPs )
    oLbx:bLine := {|| Retbline(oLbx, aIPs) }
    oLbx:Refresh()	

Return


Static Function TesteIP(cEnvServer, cIP, cPorta, nUso)
    Local oServer 
    Local nTimeOut := 10
    Local aInfoThr := {}
    
    nUso := 0

    If __cMyIp == Alltrim(cIP) .and. __cMyPort == cPorta
        nUso := len(GetUserInfoArray())
        Return .t.
    EndIf

    oServer := TRpc():New(cEnvServer)
    If ! oServer:Connect(Alltrim(cIP) , Val(cPorta) , nTimeOut )	
        Return .F.
    EndIf

    __cErroP := ""
    bErroA   := ErrorBlock( { |oErro| ChkErrP( oErro ) } ) 
    Begin Sequence
        aInfoThr := oServer:CallProc("GetUserInfoArray")
    End Sequence
	ErrorBlock(bErroA) 
    oServer:Disconnect()

    If ! Empty(__cErroP)
        __cErroP := ""
        Return .F.
    EndIf
    nUso := len(aInfoThr)

Return .T.

Static Function ExcIP(oLbx, aIPs)
    Local nSelect := oLbx:nAt

    If Len(aIPs) < 2
        MsgAlert("Necessario ter no minimo um ip")
        Return
    EndIf

    If ! MsgYesNo("Confirma a Exclusão do IP:" + aIPs[oLbx:nAt, 1] + " Porta:" + aIPs[oLbx:nAt, 2] )
        Return
    EndIf

    adel(aIPs, nSelect)
    aSize(aIPs, len(aIPs) - 1)

    oLbx:SetArray( aIPs )
    oLbx:bLine := {|| Retbline(oLbx, aIPs) }
    oLbx:Refresh()	

Return



Static Function AtuTelIP(oLbx, aIPs, oTimer, oDlg)
    Local nx   := 0
    Local nUso := 0
    Local nTDisp := 0
    
    
    If oTimer == NIL
        Return
    EndIf 
    
    oTimer:Deactivate()   
    
    For nx:= 1 to len(aIPs)
        ProcessMessage()
        If TesteIP(aIPs[nx, 1], aIPs[nx, 2], aIPs[nx, 3], @nUso)
            aIPs[nx, 8] := "Ok!"
        Else
            aIPs[nx, 8] := "Não conecta!"
        EndIf
        aIPs[nx, 5] := nUso
        aIPs[nx, 6] := Max(aIPs[nx, 4] - nUso, 0)
        If Left(aIPs[nx, 7], 1) == "E"
            nTDisp += aIPs[nx, 6] 
        EndIf
    Next
    
    oLbx:SetArray( aIPs )
    oLbx:bLine := {|| Retbline(oLbx, aIPs) }	
    oLbx:Refresh()
  
    oDlg:cCaption := "Configuração de IPs  - Disponibilidade de " + Alltrim(Str(nTDisp)) + " Threads para execução "
  
    oTimer:Activate()

Return


Static Function Monitor(aIPs, oLbxIp, oTimerAnt)
    Local oPanelM1
    Local oLbx
    Local oRect
    Local oDlg
    

    Local cEnvServer := Padr(aIPs[oLbxIp:nAt, 1], 60)
    Local cServerIP  := Alltrim(aIPs[oLbxIp:nAt, 2])
    Local nPortaTcp  := Val(aIPs[oLbxIp:nAt, 3])
    Local oTimer

    Local aLista := SrvInfoUser(cServerIP, nPortaTcp, Alltrim(cEnvServer))

    Local aCab   := {  "Usuário" ,;
                        "Máquina local" ,;
                        "Thread" ,;
                        "Balance" ,;
                        "Função" ,;
                        "Ambiente" ,;
                        "Data e hora" ,;
                        "Ativa" ,;
                        "Instruções" ,;
                        "Instruções em Seg. " ,;
                        "Observações" ,;
                        "Memória (bytes)" ,;
                        "SID " ,;
                        "Identificador de processo" ,;
                        "Tipo" ,;
                        "Tempo de inatividade"}

    If oTimerAnt == NIL
       Return
    EndIf 

    
    
    If Empty(aLista)
        aadd(aLista, {"", "",0 , "", "",	"",	"",	"",	0 ,	0 ,	"",	"" , 0 , "" , 0, "" , ""})
    EndIf
                    
    oTimerAnt:Deactivate()   

    oRect := TRect():New(0, 0, oMainWnd:nBottom * 0.70 , oMainWnd:nRight * 0.90)	
        
    DEFINE MSDIALOG oDlg FROM 0,0 TO 0,0 TITLE "Monitor threads  Alt-I"  //STYLE nOR(WS_VISIBLE,WS_POPUP)
        oDlg:SetCoors(oRect)
    
        oPanelM1 := TPanelCss():New(,,,oDlg)
        oPanelM1 :SetCoors(TRect():New( 0,0, 30, 30))
        oPanelM1 :Align := CONTROL_ALIGN_TOP
            @ 05,002 SAY "Ambiente:"  of oPanelM1 SIZE 030,09 PIXEL
            @ 02,035 GET cEnvServer   of oPanelM1 SIZE 080,09 PIXEL PICTURE "@!" WHEN lChangeIP

            @ 05,120 SAY "Ip Server:"  of oPanelM1 SIZE 030,09 PIXEL
            @ 02,155 GET cServerIP     of oPanelM1 SIZE 080,09 PIXEL PICTURE "@!" WHEN lChangeIP

            @ 05,240 SAY "Porta:"   of oPanelM1 SIZE 030,09 PIXEL
            @ 02,275 GET nPortaTcp  of oPanelM1 SIZE 040,09 PIXEL PICTURE "99999" WHEN lChangeIP

            @ 02, 320 BUTTON oBut PROMPT 'Finalizar todas'         SIZE 045,010 ACTION DellAll( aLista, cEnvServer, cServerIP, nPortaTcp)   OF oPanelM1 PIXEL ; oBut:nClrText :=0

        
        oLbx:= TwBrowse():New(01,01,490,490,,aCab,, oDlg,,,,,,,,,,,,.F.,,.T.,,.F.,,,)
        oLbx:align := CONTROL_ALIGN_ALLCLIENT     
        oLbx:SetArray( aLista )
        oLbx:bLine := {|| Retbline(oLbx, aLista) }	
        oLbx:bLDblClick 	:= { || DelThread(aLista, oLbx:nAt, cEnvServer, cServerIP, nPortaTcp) }
        oLbx:Refresh()	

        DEFINE TIMER oTimer INTERVAL 3000 ACTION AtuMT(oLbx, aLista, oDlg, cEnvServer, cServerIP, nPortaTcp, oTimer) OF oDlg

    ACTIVATE MSDIALOG oDlg ON INIT (AtuMT(oLbx, aLista, oDlg, cEnvServer, cServerIP, nPortaTcp, oTimer), oTimer:Activate())  CENTERED  

    oTimerAnt:Activate()

    

Return 

Static Function AtuMT(oLbx, aLista, oDlg, cEnvServer, cServerIP, nPortaTcp, oTimer)
            
    If oTimer == NIL
        Return
    EndIf 
    
    oTimer:Deactivate()   
    
	aLista := SrvInfoUser(cServerIP, nPortaTcp, Alltrim(cEnvServer))
    
    If Empty(aLista)
        aadd(aLista, {"", "",0 , "",	"",	"",	"",	"",	0 ,	0 ,	"",	"" , 0 , "" , 0, "" , ""})
    EndIf
    
    oLbx:SetArray( aLista )
    oLbx:bLine := {|| Retbline(oLbx,aLista) }	
    oLbx:Refresh()

    oTimer:Activate()

Return

Static Function SrvInfoUser(cServerIP, nPortaTcp, cEnvServer)
    Local nTimeOut  := 10
    Local oServer 
    Local aInfoThr  := {}
    Local bErroA    
    
    oServer := TRpc():New(cEnvServer)
    If ! oServer:Connect(Alltrim(cServerIP) , nPortaTcp , nTimeOut )	
        Return {}
    EndIf
	__cErroP := ""
	bErroA   := ErrorBlock( { |oErro| ChkErrP( oErro ) } ) 
	Begin Sequence
    	aInfoThr := oServer:CallProc("GetUserInfoArray")
	End Sequence
	ErrorBlock( bErroA ) 
	oServer:Disconnect()
    If ! Empty(__cErroP)
        __cErroP := ""
        Return {}
    EndIf

Return aInfoThr


Static Function DelThread(aLista, nAt, cEnvServer, cServerIP, nPortaTcp)
	Local cUserName     := aLista[nAt, 1]
	Local cComputerName := aLista[nAt, 2]
	Local nThreadId     := aLista[nAt, 3]
	Local oServer
	Local nTimeOut      := 10
    Local bErroA

	If ! MsgYesno("Finalizar a thread [" + Alltrim(str(nThreadId)) + "]  do Usuario [" +cUserName + "]?")
		return
	EndIf

	If Alltrim(cEnvServer) == GetEnvserver() .and.  cServerIP  == PegaIP() .and.  nPortaTcp == GetServerPort() .and. nThreadId == ThreadId()
		MsgStop("Essa é a sua thread e não pode ser finalizada!")
		Return
    EndIf
    
	oServer := TRpc():New(cEnvServer)
    If ! oServer:Connect(Alltrim(cServerIP) , nPortaTcp , nTimeOut )	
        Return 
    EndIf
	__cErroP := ""
	bErroA   := ErrorBlock( { |oErro| ChkErrP( oErro ) } ) 
	Begin Sequence
    	oServer:CallProc("KillUser", cUserName, cComputerName, nThreadId,  cServerIP )
	End Sequence
	ErrorBlock( bErroA ) 
	oServer:Disconnect()
    If ! Empty(__cErroP)
        __cErroP := ""
    EndIf
	
Return

Static Function DellAll( aLista, cEnvServer, cServerIP, nPortaTcp)
	Local cUserName     := ""
	Local cComputerName := ""
	Local nThreadId     := ""
	Local oServer
	Local nTimeOut      := 10
	Local bErroA
    Local nx

	If ! MsgNoYes("Finalizar todas a threads?")
		return
    EndIf
    
	oServer := TRpc():New(cEnvServer)
    If ! oServer:Connect(Alltrim(cServerIP) , nPortaTcp , nTimeOut )	
        Return 
    EndIf
	
	__cErroP := ""
	bErroA   := ErrorBlock( { |oErro| ChkErrP( oErro ) } ) 
	Begin Sequence
		For nx := 1 to len(aLista)
			cUserName     := aLista[nx, 1]
			cComputerName := aLista[nx, 2]
			nThreadId     := aLista[nx, 3]
			If Alltrim(cEnvServer) == GetEnvserver() .and.  cServerIP  == PegaIP() .and.  nPortaTcp == GetServerPort() .and. nThreadId == ThreadId()
				Loop
			EndIf
    		oServer:CallProc("KillUser", cUserName, cComputerName, nThreadId,  cServerIP )
		Next
	End Sequence
	ErrorBlock( bErroA ) 
	
	oServer:Disconnect()
    If ! Empty(__cErroP)
        __cErroP := ""
    EndIf

Return


Static Function CriaCfg(cChave)
    Local cFileAnt := "system\tijob\executores.cfg"
    Local cFilecfg := "system\tijob\" + cChave + ".cfg"
    Local cEnv     := GetEnvserver()
    Local cIP      := PegaIP()  
    Local cPorta   := AllTrim(GetPvProfString("TCP", "Port", "0", GetAdv97()))
    Local aLstIP   := {}
    Local cJson    := ""

    If File(cFilecfg)
        Return
    EndIf
    If File(cFileAnt)
       Memowrit(cFilecfg, MemoRead(cFileAnt))
       Return 
    EndIf

    aadd(aLstIP, {cEnv, cIP, cPorta, 50, 0, 0, "GERENCIADOR", ""})
    aadd(aLstIP, {cEnv, cIP, cPorta, 50, 0, 0,  "EXECUTOR"  , ""})
    cJson := FwJsonSerialize(aLstIP)
    Memowrit(cFilecfg, cJson)

Return

Static Function LoadCfg(cChave)
    Local cFilecfg := "system\tijob\" + cChave + ".cfg"
    Local cParam   := ""
    Local aIPs     := {}
    Local aLstIP   := {}
    Local nx       := 0
    Local nUso     := 0
    Local cEnv     := GetEnvserver()
    Local cIP      := PegaIP()  
    Local cPorta   := AllTrim(GetPvProfString("TCP", "Port", "0", GetAdv97()))

    

    If FindFunction("U_XJOBDEV")  //Para teste local em ambiente de desenvolvimento. Basta criar compilar o PE.
        aadd(aIPs, {cEnv, cIP, cPorta, 3, 0, 0, "GERENCIADOR", "Ok!"})
        aadd(aIPs, {cEnv, cIP, cPorta, 7, 0, 0,  "EXECUTOR"  , "Ok!"})
    Else
        cParam := Alltrim(MemoRead(cFilecfg))
        FWJsonDeserialize(cParam, @aIPs)
    EndIf

    For nx:= 1 to Len(aIPs)
        If aIPs[nx, 7] == "GERENCIADOR"
            __cEnvSrv := aIPs[nx, 1]


            __cIPSrv  := aIPs[nx, 2]
            __cPorSrv := aIPs[nx, 3]
    
            loop 
        EndIf
    
        If TesteIP(aIPs[nx, 1], aIPs[nx, 2], aIPs[nx, 3], @nUso)
            aIPs[nx, 5] := nUso
            aIPs[nx, 6] := Max(aIPs[nx, 4] - nUso, 0)
            aIPs[nx, 8] := "Ok!"
            aadd(aLstIP, aclone(aIPs[nx]))
        EndIf
    Next
    __aIPExec := aLstIP
    
Return 

Static Function InfoConnect()

    If __cMyPort == NIL
        __cMyPort  := AllTrim(Str(GetServerPort()))
    EndIf
    If __cMyIp == NIL
        __cMyIp := PegaIP()
    EndIf
Return 


User Function JSaveQry(cQuery)
    Local cArqQry   := "query.sql"

    GrvArq(cArqQry, cQuery)

Return

Static Function MostraQry()
    Local cArqQry  := "query.sql"

    MostraLog(cArqQry)    

Return

Static Function PegaIP()
    Local cIP := ""
    Local aIP := GetServerIP(.T.)
    Local nx  
    

    For nx := 1 to Len(aIP)
        If Left(aIP[nx, 4], 3) == "172"	
            cIP := aIP[nx, 4]
        EndIf
    Next

Return cIP


User Function JGraIni(cChave)
    Local aArea := GetArea()
    Local aAreaZX5 := ZX5->(GetArea())
    
    __cIDGra := ""
    ZX5->(DbSetOrder(2))
    If ZX5->(DbSeek(xFilial("ZX5") + "TIJOBS"))
        While ZX5->(!EOF()) .and. ZX5->ZX5_TABELA == "TIJOBS"
            If cChave == AllTrim(ZX5->ZX5_COMPL)
                __cIDGra := u_tiIniJob(cChave, .f.)
                Exit
            EndIf
            ZX5->(DbSkip()) 
        End
    EndIf 

    RestArea(aAreaZX5)
    RestArea(aArea)
    
Return 

User Function JGraQtd(cChave, nQtdReg)
    If Empty(__cIDGra)
        Return 
    EndIf  
    u_tiAtuJob(__cIDGra, 1, nQtdReg)
Return 

User Function JGraProc(cChave)
    If Empty(__cIDGra)
        Return 
    EndIf  
    u_tiAtuJob(__cIDGra, 2)
Return 

User Function JGraEnd(cChave)
    If Empty(__cIDGra)
        Return 
    EndIf  
    u_tiEndJob(__cIDGra)
Return 

